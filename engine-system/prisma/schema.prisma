generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model NotificationLog {
  id           BigInt             @id @default(autoincrement()) @db.UnsignedBigInt
  triggerId    Int?               @map("trigger_id") @db.UnsignedInt
  templateId   Int?               @map("template_id") @db.UnsignedInt
  idSite       Int                @map("id_site") @db.UnsignedInt
  channelId    Int                @map("channel_id") @db.UnsignedTinyInt
  recipient    String             @db.VarChar(512)
  status       NotificationStatus
  triggerValue String             @map("trigger_value") @db.VarChar(64)
  metadata     Json?
  errorMessage String?            @map("error_message") @db.Text
  createdAt    DateTime           @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt    DateTime           @default(now()) @map("updated_at") @db.Timestamp(0)
  createdDate  DateTime?          @map("created_date") @db.DateTime(0)
  trigger      Trigger?           @relation(fields: [triggerId], references: [id], map: "notification_logs_ibfk_1")
  templates    Template?          @relation(fields: [templateId], references: [id], map: "notification_logs_ibfk_2")
  channelType  ChannelType        @relation(fields: [channelId], references: [id], map: "notification_logs_ibfk_3")

  @@index([createdDate], map: "idx_notification_logs_created_date")
  @@index([idSite, channelId, triggerValue], map: "idx_notification_logs_site_channel_trigger_value")
  @@index([idSite, createdDate], map: "idx_notification_logs_site_created_date")
  @@index([idSite, triggerId], map: "idx_notification_logs_site_trigger")
  @@index([triggerId], map: "idx_notification_logs_trigger_id")
  @@index([status], map: "idx_notification_logs_status")
  @@index([templateId], map: "idx_notification_logs_template_id")
  @@index([channelId], map: "idx_notification_logs_channel_id")
  @@map("notification_logs")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model Trigger {
  id                          Int                          @id @default(autoincrement()) @db.UnsignedInt
  name                        String                       @unique(map: "uq_triggers_name") @db.VarChar(255)
  description                 String?                      @db.Text
  triggerTypeId               Int                          @map("trigger_type_id") @db.UnsignedTinyInt
  metricKey                   String?                      @map("metric_key") @db.VarChar(255)
  isConfigurable              Boolean                      @default(false) @map("is_configurable")
  metadata                    Json?
  cooldownSeconds             Int?                         @map("cooldown_seconds")
  publicId                    String                       @unique(map: "uq_triggers_public_id") @default(dbgenerated("(uuid())")) @map("public_id") @db.Char(36)
  maxTriggerCount             Int?                         @map("max_trigger_count") @db.UnsignedInt
  maxTriggerPeriod            TriggerIntervalPeriod?       @map("max_trigger_period")
  minIntervalCount            Int?                         @map("min_interval_count") @db.UnsignedInt
  minIntervalUnit             TriggerIntervalPeriod?       @map("min_interval_unit")
  fireOnce                    Boolean                      @default(false) @map("fire_once")
  deltaThreshold              Decimal?                     @map("delta_threshold") @db.Decimal(5, 2)
  createdAt                   DateTime                     @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt                   DateTime                     @default(now()) @map("updated_at") @db.Timestamp(0)
  createdBy                   String                       @default("system") @map("created_by") @db.VarChar(128)
  updatedBy                   String                       @default("system") @map("updated_by") @db.VarChar(128)
  deletedAt                   DateTime?                    @map("deleted_at") @db.Timestamp(0)
  notificationLogs            NotificationLog[]
  parameters                  Parameter[]
  siteNotificationPreferences SiteNotificationPreference[]
  siteTriggerSettings         SiteTriggerSetting[]
  templates                   Template[]
  triggerType                 TriggerType                  @relation(fields: [triggerTypeId], references: [id], map: "triggers_ibfk_1")

  @@index([deletedAt], map: "idx_triggers_deleted_at")
  @@index([triggerTypeId], map: "idx_triggers_trigger_type")
  @@map("triggers")
}

model Parameter {
  id            Int           @id @default(autoincrement()) @db.UnsignedInt
  triggerId     Int?          @map("trigger_id") @db.UnsignedInt
  templateId    Int?          @map("template_id") @db.UnsignedInt
  name          String        @db.VarChar(255)
  description   String?       @db.Text
  required      Boolean       @default(false)
  paramTypeId   Int           @map("param_type_id") @db.UnsignedTinyInt
  validations   Json?
  defaultValue  String?       @map("default_value") @db.Text
  exampleValue  String?       @map("example_value") @db.Text
  createdAt     DateTime      @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt     DateTime      @default(now()) @map("updated_at") @db.Timestamp(0)
  trigger       Trigger?      @relation(fields: [triggerId], references: [id], onDelete: Cascade, map: "parameters_ibfk_1")
  template      Template?     @relation(fields: [templateId], references: [id], onDelete: Cascade, map: "parameters_ibfk_2")
  parameterType ParameterType @relation(fields: [paramTypeId], references: [id], map: "parameters_ibfk_3")

  @@unique([triggerId, templateId, name], map: "uq_parameter_name_per_scope")
  @@index([templateId, name], map: "idx_parameters_template_name")
  @@index([triggerId, name], map: "idx_parameters_trigger_name")
  @@index([paramTypeId], map: "idx_parameters_param_type_id")
  @@map("parameters")
}

model SiteNotificationPreference {
  id                Int         @id @default(autoincrement()) @db.UnsignedInt
  idSite            Int         @map("id_site") @db.UnsignedInt
  channelId         Int         @map("channel_id") @db.UnsignedTinyInt
  triggerId         Int?        @map("trigger_id") @db.UnsignedInt
  isEnabled         Boolean     @default(false) @map("is_enabled")
  destination       Json?
  createdAt         DateTime    @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt         DateTime    @default(now()) @map("updated_at") @db.Timestamp(0)
  deletedAt         DateTime?   @map("deleted_at") @db.Timestamp(0)
  triggerNormalized Int?        @map("trigger_normalized") @db.UnsignedInt
  channelType       ChannelType @relation(fields: [channelId], references: [id], map: "site_notification_preferences_ibfk_1")
  triggers          Trigger?    @relation(fields: [triggerId], references: [id], onDelete: Restrict, map: "fk_site_notification_preferences_trigger")

  @@unique([idSite, channelId, triggerNormalized], map: "uq_site_notification_preferences_site_channel_trigger")
  @@index([deletedAt], map: "idx_site_notification_preferences_deleted_at")
  @@index([channelId], map: "channel_id")
  @@index([triggerId], map: "trigger_id")
  @@map("site_notification_preferences")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model SiteTriggerSetting {
  id                       Int                    @id @default(autoincrement()) @db.UnsignedInt
  idSite                   Int                    @map("id_site") @db.UnsignedInt
  triggerId                Int                    @map("trigger_id") @db.UnsignedInt
  isEnabled                Boolean                @default(false) @map("is_enabled")
  conditionOverride        Json?                  @map("condition_override")
  minIntervalCountOverride Int?                   @map("min_interval_count_override") @db.UnsignedInt
  minIntervalUnitOverride  TriggerIntervalPeriod? @map("min_interval_unit_override")
  maxTriggerCountOverride  Int?                   @map("max_trigger_count_override") @db.UnsignedInt
  maxTriggerPeriodOverride TriggerIntervalPeriod? @map("max_trigger_period_override")
  deltaThresholdOverride   Decimal?               @map("delta_threshold_override") @db.Decimal(5, 2)
  createdAt                DateTime               @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt                DateTime               @default(now()) @map("updated_at") @db.Timestamp(0)
  deletedAt                DateTime?              @map("deleted_at") @db.Timestamp(0)
  triggers                 Trigger                @relation(fields: [triggerId], references: [id], map: "site_trigger_settings_ibfk_1")

  @@unique([idSite, triggerId], map: "uq_site_trigger_settings_site_trigger")
  @@index([triggerId], map: "trigger_id")
  @@index([deletedAt], map: "idx_site_trigger_settings_deleted_at")
  @@map("site_trigger_settings")
}

model TemplateAuditLog {
  id          BigInt               @id @default(autoincrement()) @db.UnsignedBigInt
  templateId  Int                  @map("template_id") @db.UnsignedInt
  action      TemplateAuditAction?
  fromVersion Int?                 @map("from_version")
  toVersion   Int?                 @map("to_version")
  performedBy String?              @map("performed_by") @db.VarChar(255)
  reason      String?              @db.Text
  createdAt   DateTime             @default(now()) @map("created_at") @db.Timestamp(0)
  template    Template             @relation(fields: [templateId], references: [id], onDelete: Cascade, map: "template_audit_logs_ibfk_1")

  @@index([performedBy], map: "idx_template_audit_logs_performed_by")
  @@index([templateId, toVersion], map: "idx_template_audit_logs_template_version")
  @@map("template_audit_logs")
}

model Template {
  id                Int                @id @default(autoincrement()) @db.UnsignedInt
  triggerId         Int?               @map("trigger_id") @db.UnsignedInt
  name              String             @db.VarChar(255)
  description       String?            @db.Text
  version           Int                @default(1)
  channelId         Int                @map("channel_id") @db.UnsignedTinyInt
  subject           String?            @db.VarChar(255)
  body              String             @db.Text
  contentTypeId     Int                @map("content_type_id") @db.UnsignedTinyInt
  metadata          Json?
  status            TemplateStatus     @default(draft)
  publicId          String             @unique(map: "uq_templates_public_id") @default(dbgenerated("(uuid())")) @map("public_id") @db.Char(36)
  createdAt         DateTime           @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt         DateTime           @default(now()) @map("updated_at") @db.Timestamp(0)
  deletedAt         DateTime?          @map("deleted_at") @db.Timestamp(0)
  createdBy         String?            @map("created_by") @db.VarChar(255)
  updatedBy         String?            @map("updated_by") @db.VarChar(255)
  notificationLogs  NotificationLog[]
  parameters        Parameter[]
  templateAuditLogs TemplateAuditLog[]
  trigger           Trigger?           @relation(fields: [triggerId], references: [id], onDelete: Restrict, map: "templates_ibfk_1")
  contentType       ContentType        @relation(fields: [contentTypeId], references: [id], map: "templates_ibfk_2")
  channelType       ChannelType        @relation(fields: [channelId], references: [id], map: "templates_ibfk_3")

  @@unique([triggerId, channelId, version], map: "uq_templates_trigger_channel")
  @@index([deletedAt], map: "idx_templates_deleted_at")
  @@index([channelId], map: "idx_templates_channel_id")
  @@index([contentTypeId], map: "idx_templates_content_type_id")
  @@map("templates")
}

model ChannelType {
  id                          Int                          @id @default(autoincrement()) @db.UnsignedTinyInt
  name                        String?                      @unique(map: "uq_channel_types_name") @db.VarChar(64)
  description                 String?                      @db.VarChar(255)
  createdAt                   DateTime                     @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt                   DateTime                     @default(now()) @map("updated_at") @db.Timestamp(0)
  notificationLogs            NotificationLog[]
  siteNotificationPreferences SiteNotificationPreference[]
  siteTokens                  SiteToken[]
  templates                   Template[]

  @@map("channel_types")
}

model ContentType {
  id          Int        @id @default(autoincrement()) @db.UnsignedTinyInt
  name        String     @unique(map: "uq_content_types_name") @db.VarChar(64)
  description String?    @db.VarChar(255)
  createdAt   DateTime   @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime   @default(now()) @map("updated_at") @db.Timestamp(0)
  templates   Template[]

  @@map("content_types")
}

model ParameterType {
  id          Int         @id @default(autoincrement()) @db.UnsignedTinyInt
  name        String      @unique(map: "parameter_types_name") @db.VarChar(64)
  description String?     @db.VarChar(255)
  createdAt   DateTime    @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime    @default(now()) @map("updated_at") @db.Timestamp(0)
  parameters  Parameter[]

  @@map("parameter_types")
}

model TriggerType {
  id          Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  name        String    @unique(map: "uq_trigger_types_name") @db.VarChar(64)
  description String?   @db.VarChar(255)
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime  @default(now()) @map("updated_at") @db.Timestamp(0)
  triggers    Trigger[]

  @@map("trigger_types")
}

model SiteToken {
  id                   Int         @id @default(autoincrement()) @db.UnsignedInt
  idSite               Int         @map("id_site") @db.UnsignedInt
  channelId            Int         @map("channel_id") @db.UnsignedTinyInt
  accessTokenEncrypted String      @map("access_token_encrypted") @db.Text
  iv                   String      @db.Char(32)
  tag                  String      @db.Char(32)
  createdAt            DateTime?   @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt            DateTime?   @default(now()) @map("updated_at") @db.Timestamp(0)
  deletedAt            DateTime?   @map("deleted_at") @db.Timestamp(0)
  teamId               String?     @map("team_id") @db.VarChar(16)
  channelType          ChannelType @relation(fields: [channelId], references: [id], map: "site_tokens_ibfk_1")

  @@unique([idSite, channelId], map: "uq_site_tokens_site_channel")
  @@index([channelId], map: "channel_id")
  @@index([createdAt], map: "idx_site_tokens_created_at")
  @@map("site_tokens")
}

enum NotificationStatus {
  pending
  sent
  failed
}

enum TemplateAuditAction {
  created
  rolled_back
  published
  archived
  edited
}

enum TriggerIntervalPeriod {
  minute
  hour
  day
  week
  month
  year
}

enum TemplateStatus {
  draft
  archived
  published
}
