<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Project Overview - Complete system architecture, features, and getting started guide">
    <title>Project Overview - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/styles.css">
    <link rel="stylesheet" href="../assets/doc-page.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html" class="logo-link">
                        <h1 class="logo-text">
                            <span class="logo-icon">🔔</span>
                            Notification Engine
                        </h1>
                        <p class="logo-subtitle">Documentation Hub</p>
                    </a>
                </div>
                
                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="../index.html#getting-started" class="nav-link">Getting Started</a></li>
                        <li><a href="../index.html#api-integration" class="nav-link">API & Integration</a></li>
                        <li><a href="../index.html#architecture" class="nav-link">Architecture</a></li>
                        <li><a href="../index.html#operations" class="nav-link">Operations</a></li>
                        <li><a href="../index.html#development" class="nav-link">Development</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <a href="../index.html" class="btn btn-secondary">← Back to Home</a>
                    <a href="../api/api-reference.html" class="btn btn-primary">API Reference</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../index.html#getting-started">Getting Started</a></li>
                        <li aria-current="page">Project Overview</li>
                    </ol>
                </nav>
                
                <h1 class="page-title">Project Overview</h1>
                <p class="page-description">
                    Complete system overview including architecture, features, and core concepts of the Notification Engine
                </p>
            </div>

            <!-- Table of Contents -->
            <div class="toc-container">
                <h2 class="toc-title">Table of Contents</h2>
                <nav class="toc" aria-label="Table of contents">
                    <ul class="toc-list">
                        <li><a href="#introduction">Introduction</a></li>
                        <li><a href="#system-architecture">System Architecture</a></li>
                        <li><a href="#core-features">Core Features</a></li>
                        <li><a href="#technology-stack">Technology Stack</a></li>
                        <li><a href="#project-structure">Project Structure</a></li>
                        <li><a href="#getting-started">Getting Started</a></li>
                        <li><a href="#next-steps">Next Steps</a></li>
                    </ul>
                </nav>
            </div>

            <!-- Content Sections -->
            <div class="content">
                <section id="introduction" class="content-section">
                    <h2>Introduction</h2>
                    <p>
                        The Notification Engine is a comprehensive, enterprise-grade TypeScript-based system designed to handle 
                        multi-channel notifications, trigger evaluations, and template management for SaaS applications. Built with 
                        scalability, reliability, and maintainability in mind, it serves as the backbone for automated communication 
                        across email, Slack, and CRM platforms.
                    </p>

                    <div class="highlight-box">
                        <h3>Key Benefits</h3>
                        <ul>
                            <li><strong>Multi-Channel Support:</strong> Unified interface for email, Slack, and HubSpot notifications</li>
                            <li><strong>Template-Driven:</strong> Flexible Handlebars-based templating with layout inheritance</li>
                            <li><strong>Queue-Based Processing:</strong> Reliable message processing with retry mechanisms</li>
                            <li><strong>Real-Time & Scheduled:</strong> Support for both immediate and scheduled notifications</li>
                            <li><strong>Comprehensive Auditing:</strong> Full audit trails for compliance and debugging</li>
                            <li><strong>Site-Specific Customization:</strong> Per-site configuration and preferences</li>
                            <li><strong>Developer-Friendly:</strong> Well-documented APIs and extensive testing support</li>
                        </ul>
                    </div>
                </section>

                <section id="system-architecture" class="content-section">
                    <h2>System Architecture</h2>
                    <p>
                        The Notification Engine follows a modular, microservices-inspired architecture with clear separation of concerns:
                    </p>

                    <div class="architecture-overview">
                        <div class="arch-diagram">
                            <div class="arch-layer">
                                <div class="arch-component api">
                                    <h4>API Gateway</h4>
                                    <ul>
                                        <li>REST APIs</li>
                                        <li>Webhooks</li>
                                        <li>Admin Panel</li>
                                    </ul>
                                </div>
                                <div class="arch-component queue">
                                    <h4>Queue System</h4>
                                    <ul>
                                        <li>BullMQ/Redis</li>
                                        <li>RabbitMQ</li>
                                        <li>Job Queues</li>
                                    </ul>
                                </div>
                                <div class="arch-component connectors">
                                    <h4>Connectors</h4>
                                    <ul>
                                        <li>Email (SMTP)</li>
                                        <li>Slack API</li>
                                        <li>HubSpot CRM</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="arch-layer">
                                <div class="arch-component database">
                                    <h4>Database</h4>
                                    <ul>
                                        <li>MySQL/Prisma</li>
                                        <li>Notifications</li>
                                        <li>Templates</li>
                                        <li>Triggers</li>
                                    </ul>
                                </div>
                                <div class="arch-component templates">
                                    <h4>Template Engine</h4>
                                    <ul>
                                        <li>Handlebars</li>
                                        <li>Email/Slack</li>
                                        <li>Validation</li>
                                        <li>Rendering</li>
                                    </ul>
                                </div>
                                <div class="arch-component external">
                                    <h4>External Services</h4>
                                    <ul>
                                        <li>Site APIs</li>
                                        <li>Analytics</li>
                                        <li>Integrations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3>Core Components</h3>
                    <ol>
                        <li><strong>API Layer</strong> - Express.js REST API with comprehensive middleware</li>
                        <li><strong>Queue System</strong> - Dual-queue architecture (BullMQ + RabbitMQ)</li>
                        <li><strong>Template Engine</strong> - Handlebars-based templating with custom helpers</li>
                        <li><strong>Database Layer</strong> - Prisma ORM with MySQL for data persistence</li>
                        <li><strong>Connectors</strong> - Channel-specific integrations for message delivery</li>
                        <li><strong>Job Processors</strong> - Background workers for notification processing</li>
                    </ol>
                </section>

                <section id="core-features" class="content-section">
                    <h2>Core Features</h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>📧 Notification Management</h3>
                            <ul>
                                <li>Multi-Channel Delivery: Email, Slack, HubSpot CRM integration</li>
                                <li>Template-Based Messaging: Handlebars templates with layout support</li>
                                <li>Real-Time Notifications: Immediate delivery for urgent messages</li>
                                <li>Scheduled Notifications: Cron-based scheduling for recurring messages</li>
                                <li>Retry Mechanisms: Automatic retry with exponential backoff</li>
                                <li>Delivery Tracking: Comprehensive logging and status tracking</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>📝 Template System</h3>
                            <ul>
                                <li>Handlebars Templating: Rich templating with custom helpers</li>
                                <li>Layout Inheritance: Consistent branding across channels</li>
                                <li>Multi-Channel Support: Channel-specific template variations</li>
                                <li>Parameter Validation: Zod schema validation for template data</li>
                                <li>Hot Reloading: Development-time template updates</li>
                                <li>Version Control: Template versioning and rollback capabilities</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>🔄 Queue Management</h3>
                            <ul>
                                <li>Dual-Queue Architecture: BullMQ for jobs, RabbitMQ for messaging</li>
                                <li>Job Prioritization: Priority-based job processing</li>
                                <li>Concurrency Control: Configurable worker concurrency</li>
                                <li>Dead Letter Queues: Failed job handling and recovery</li>
                                <li>Monitoring Dashboard: Real-time queue monitoring via Bull Board</li>
                                <li>Rate Limiting: Configurable rate limits per channel</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>⚙️ Administration</h3>
                            <ul>
                                <li>Health Monitoring: Comprehensive health checks and metrics</li>
                                <li>Audit Logging: Complete audit trails for compliance</li>
                                <li>Configuration Management: Environment-based configuration</li>
                                <li>Queue Dashboard: Visual queue monitoring and management</li>
                                <li>Template Management: CRUD operations for templates and triggers</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section id="technology-stack" class="content-section">
                    <h2>Technology Stack</h2>
                    
                    <div class="tech-grid">
                        <div class="tech-category">
                            <h3>Core Technologies</h3>
                            <ul>
                                <li><strong>Runtime:</strong> Node.js 18+ with TypeScript</li>
                                <li><strong>Framework:</strong> Express.js with comprehensive middleware</li>
                                <li><strong>Database:</strong> MySQL 8.0+ with Prisma ORM</li>
                                <li><strong>Queue System:</strong> BullMQ (Redis) + RabbitMQ</li>
                                <li><strong>Template Engine:</strong> Handlebars.js with custom helpers</li>
                                <li><strong>Validation:</strong> Zod schemas for type-safe validation</li>
                            </ul>
                        </div>

                        <div class="tech-category">
                            <h3>Key Dependencies</h3>
                            <ul>
                                <li><strong>@bull-board/express:</strong> Queue monitoring dashboard</li>
                                <li><strong>@slack/web-api:</strong> Slack integration and API client</li>
                                <li><strong>nodemailer:</strong> Email sending with SMTP support</li>
                                <li><strong>axios:</strong> HTTP client for external API calls</li>
                                <li><strong>helmet:</strong> Security middleware for Express</li>
                                <li><strong>ioredis:</strong> High-performance Redis client</li>
                                <li><strong>amqplib:</strong> RabbitMQ client for message queuing</li>
                            </ul>
                        </div>

                        <div class="tech-category">
                            <h3>Development Tools</h3>
                            <ul>
                                <li><strong>tsx:</strong> TypeScript execution for development</li>
                                <li><strong>copyfiles:</strong> Build asset copying for templates</li>
                                <li><strong>pino:</strong> Structured logging with performance focus</li>
                                <li><strong>dotenv:</strong> Environment configuration management</li>
                                <li><strong>prettier:</strong> Code formatting and style consistency</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section id="project-structure" class="content-section">
                    <h2>Project Structure</h2>
                    <p>The project follows a well-organized structure with clear separation of concerns:</p>
                    
                    <div class="code-block">
                        <pre><code>engine-system/
├── src/                        # Source code
│   ├── server.ts              # Application entry point
│   ├── app.ts                 # Express app configuration
│   ├── db.ts                  # Database connection setup
│   ├── env.constant.ts        # Environment validation
│   ├── template-engine.ts     # Template processing engine
│   ├── template-registry.ts   # Template definitions and schemas
│   ├── api/                   # REST API layer
│   │   ├── controllers/       # Request handlers and business logic
│   │   ├── routes/           # Route definitions and middleware
│   │   ├── middlewares.ts    # Custom Express middleware
│   │   ├── schemas/          # API validation schemas
│   │   └── utils/            # API utility functions
│   ├── jobs/                 # Queue job definitions
│   │   ├── evaluation/       # Site evaluation jobs
│   │   ├── trigger/          # Trigger processing jobs
│   │   ├── notification/     # Notification delivery jobs
│   │   ├── bot/             # Bot automation jobs
│   │   └── observation/     # Analytics and monitoring jobs
│   ├── messaging/           # Queue and messaging infrastructure
│   │   ├── queue-service.ts # RabbitMQ service implementation
│   │   ├── consumers/       # Message consumers
│   │   └── producers/       # Message producers
│   ├── connectors/          # External service integrations
│   │   ├── smtp.ts         # Email connector implementation
│   │   ├── slack.ts        # Slack connector and API wrapper
│   │   └── hubspot.ts      # HubSpot CRM connector
│   ├── services/           # Business logic services
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Shared utility functions
├── docs/                   # Comprehensive documentation
├── templates/              # Handlebars templates
├── prisma/                 # Database schema and migrations
├── tests/                  # Test suites
└── docker-compose.yml      # Docker development setup</code></pre>
                    </div>
                </section>

                <section id="getting-started" class="content-section">
                    <h2>Getting Started</h2>
                    
                    <h3>Prerequisites</h3>
                    <ul>
                        <li>Node.js 18+</li>
                        <li>MySQL 8.0+</li>
                        <li>Redis 6.0+</li>
                        <li>RabbitMQ 3.8+</li>
                    </ul>

                    <h3>Quick Setup</h3>
                    <div class="code-block">
                        <pre><code># Clone and install
git clone &lt;repository-url&gt;
cd notification-engine/engine-system
npm install

# Environment setup
cp env.sample .env
# Edit .env with your configuration

# Database setup
npx prisma generate
npx prisma db push
npx prisma db seed

# Start development server
npm run dev</code></pre>
                    </div>

                    <h3>Docker Setup</h3>
                    <div class="code-block">
                        <pre><code>docker-compose up -d</code></pre>
                    </div>
                </section>

                <section id="next-steps" class="content-section">
                    <h2>Next Steps</h2>
                    <p>Now that you understand the project overview, here are the recommended next steps:</p>
                    
                    <div class="next-steps-grid">
                        <a href="../development/development-guide.html" class="next-step-card">
                            <h3>🛠️ Development Setup</h3>
                            <p>Set up your development environment and understand our coding standards</p>
                        </a>
                        
                        <a href="../api/api-reference.html" class="next-step-card">
                            <h3>📚 API Reference</h3>
                            <p>Explore the complete REST API documentation with examples</p>
                        </a>
                        
                        <a href="../api/integration-guide.html" class="next-step-card">
                            <h3>🔌 Integration Guide</h3>
                            <p>Learn how to integrate with email, Slack, and HubSpot channels</p>
                        </a>
                        
                        <a href="../architecture/templates.html" class="next-step-card">
                            <h3>📝 Template System</h3>
                            <p>Understand the Handlebars templating system and template development</p>
                        </a>
                    </div>
                </section>
            </div>

            <!-- Page Navigation -->
            <nav class="page-nav" aria-label="Page navigation">
                <a href="../index.html" class="page-nav-link page-nav-prev">
                    ← Back to Documentation Home
                </a>
                <a href="../development/development-guide.html" class="page-nav-link page-nav-next">
                    Development Guide →
                </a>
            </nav>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Documentation</h4>
                    <ul class="footer-links">
                        <li><a href="project-overview.html">Project Overview</a></li>
                        <li><a href="../api/api-reference.html">API Reference</a></li>
                        <li><a href="../development/development-guide.html">Development Guide</a></li>
                        <li><a href="../operations/deployment.html">Deployment</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="../index.html">Documentation Home</a></li>
                        <li><a href="../api/integration-guide.html">Integration Examples</a></li>
                        <li><a href="../troubleshooting/troubleshooting-guide.html">Troubleshooting</a></li>
                        <li><a href="../development/contributing.html">Contributing</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Built with ❤️ for developers.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
