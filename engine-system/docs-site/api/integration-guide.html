<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta
            name="description"
            content="Notification Engine Integration Guide - Step-by-step integration examples for email, Slack, and HubSpot channels"
        />
        <title>Integration Guide - Notification Engine Documentation</title>
        <link rel="stylesheet" href="../assets/styles.css" />
        <link rel="stylesheet" href="../assets/doc-page.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <!-- Skip to main content for accessibility -->
        <a href="#main-content" class="skip-link">Skip to main content</a>

        <!-- Header Navigation -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <a href="../index.html" class="logo-link">
                            <h1 class="logo-text">
                                <span class="logo-icon">🔔</span>
                                Notification Engine
                            </h1>
                            <p class="logo-subtitle">Documentation Hub</p>
                        </a>
                    </div>

                    <nav
                        class="main-nav"
                        role="navigation"
                        aria-label="Main navigation"
                    >
                        <ul class="nav-list">
                            <li>
                                <a
                                    href="../index.html#getting-started"
                                    class="nav-link"
                                    >Getting Started</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#api-integration"
                                    class="nav-link"
                                    >API & Integration</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#architecture"
                                    class="nav-link"
                                    >Architecture</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#operations"
                                    class="nav-link"
                                    >Operations</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#development"
                                    class="nav-link"
                                    >Development</a
                                >
                            </li>
                        </ul>
                    </nav>

                    <div class="header-actions">
                        <a href="../index.html" class="btn btn-secondary"
                            >← Back to Home</a
                        >
                        <a href="api-reference.html" class="btn btn-primary"
                            >API Reference</a
                        >
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="main-content" class="main-content" role="main">
            <div class="container">
                <!-- Page Header -->
                <div class="page-header">
                    <nav class="breadcrumb" aria-label="Breadcrumb">
                        <ol class="breadcrumb-list">
                            <li><a href="../index.html">Home</a></li>
                            <li>
                                <a href="../index.html#api-integration"
                                    >API & Integration</a
                                >
                            </li>
                            <li aria-current="page">Integration Guide</li>
                        </ol>
                    </nav>

                    <h1 class="page-title">Integration Guide</h1>
                    <p class="page-description">
                        Step-by-step integration examples for email, Slack, and
                        HubSpot channels with practical code samples
                    </p>
                </div>

                <!-- Table of Contents -->
                <div class="toc-container">
                    <h2 class="toc-title">Integration Topics</h2>
                    <nav class="toc" aria-label="Table of contents">
                        <ul class="toc-list">
                            <li>
                                <a href="#quick-start"
                                    >Quick Start Integration</a
                                >
                            </li>
                            <li>
                                <a href="#email-integration"
                                    >Email Integration</a
                                >
                            </li>
                            <li>
                                <a href="#slack-integration"
                                    >Slack Integration</a
                                >
                            </li>
                            <li>
                                <a href="#hubspot-integration"
                                    >HubSpot Integration</a
                                >
                            </li>
                            <li>
                                <a href="#webhook-integration"
                                    >Webhook Integration</a
                                >
                            </li>
                            <li>
                                <a href="#best-practices">Best Practices</a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- Content Sections -->
                <div class="content">
                    <section id="quick-start" class="content-section">
                        <h2>Quick Start Integration</h2>
                        <p>
                            Get started with the Notification Engine in just a
                            few steps. This example shows how to send your first
                            notification using the REST API.
                        </p>

                        <h3>1. Authentication Setup</h3>
                        <div class="code-block">
                            <pre><code>// Set your API key
const API_KEY = 'your-api-key-here';
const BASE_URL = 'https://api.notification-engine.com/v1';

// Create headers for authentication
const headers = {
  'Authorization': `Bearer ${API_KEY}`,
  'Content-Type': 'application/json'
};</code></pre>
                        </div>

                        <h3>2. Send Your First Notification</h3>
                        <div class="code-block">
                            <pre><code>// JavaScript/Node.js example
async function sendWelcomeEmail(userEmail, userName) {
  const response = await fetch(`${BASE_URL}/notifications/send`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      templateId: 'welcome-email',
      channels: ['email'],
      recipients: {
        email: [userEmail]
      },
      data: {
        userName: userName,
        companyName: 'Your Company',
        loginUrl: 'https://yourapp.com/login'
      }
    })
  });

  const result = await response.json();
  console.log('Notification sent:', result.notificationId);
  return result;
}</code></pre>
                        </div>

                        <h3>3. Check Notification Status</h3>
                        <div class="code-block">
                            <pre><code>async function checkNotificationStatus(notificationId) {
  const response = await fetch(`${BASE_URL}/notifications/${notificationId}`, {
    headers: headers
  });

  const status = await response.json();
  console.log('Notification status:', status.status);
  return status;
}</code></pre>
                        </div>

                        <div class="highlight-box">
                            <h3>Complete Example</h3>
                            <div class="code-block">
                                <pre><code>// Complete workflow example
async function welcomeNewUser(email, name) {
  try {
    // Send welcome notification
    const notification = await sendWelcomeEmail(email, name);

    // Wait a moment and check status
    setTimeout(async () => {
      const status = await checkNotificationStatus(notification.notificationId);
      if (status.status === 'delivered') {
        console.log('Welcome email delivered successfully!');
      }
    }, 5000);

  } catch (error) {
    console.error('Failed to send welcome email:', error);
  }
}</code></pre>
                            </div>
                        </div>
                    </section>

                    <section id="email-integration" class="content-section">
                        <h2>Email Integration</h2>

                        <h3>Basic Email Notification</h3>
                        <div class="code-block">
                            <pre><code>const emailNotification = {
  templateId: 'user-invitation',
  channels: ['email'],
  recipients: {
    email: ['<EMAIL>']
  },
  data: {
    inviterName: 'John Doe',
    companyName: 'Acme Corp',
    inviteUrl: 'https://app.acme.com/accept-invite?token=abc123',
    expiresAt: '2025-06-27T23:59:59Z'
  },
  priority: 'high'
};</code></pre>
                        </div>

                        <h3>Bulk Email Notifications</h3>
                        <div class="code-block">
                            <pre><code>const bulkEmailNotification = {
  templateId: 'newsletter',
  channels: ['email'],
  recipients: {
    email: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
  },
  data: {
    newsletterTitle: 'Monthly Product Updates',
    featuredArticle: 'New Dashboard Features',
    unsubscribeUrl: 'https://app.example.com/unsubscribe'
  },
  priority: 'low'
};</code></pre>
                        </div>

                        <h3>Email with Attachments</h3>
                        <div class="code-block">
                            <pre><code>const emailWithAttachment = {
  templateId: 'invoice-email',
  channels: ['email'],
  recipients: {
    email: ['<EMAIL>']
  },
  data: {
    customerName: 'Jane Smith',
    invoiceNumber: 'INV-2025-001',
    amount: '$1,250.00',
    dueDate: '2025-07-01'
  },
  attachments: [
    {
      filename: 'invoice-2025-001.pdf',
      url: 'https://storage.example.com/invoices/2025-001.pdf',
      contentType: 'application/pdf'
    }
  ]
};</code></pre>
                        </div>
                    </section>

                    <section id="slack-integration" class="content-section">
                        <h2>Slack Integration</h2>

                        <h3>Channel Notifications</h3>
                        <div class="code-block">
                            <pre><code>const slackChannelNotification = {
  templateId: 'deployment-alert',
  channels: ['slack'],
  recipients: {
    slack: ['#deployments', '#engineering']
  },
  data: {
    environment: 'production',
    version: 'v2.1.0',
    deployedBy: 'john.doe',
    status: 'success',
    deploymentTime: '2025-06-20T15:30:00Z'
  }
};</code></pre>
                        </div>

                        <h3>Direct Messages</h3>
                        <div class="code-block">
                            <pre><code>const slackDirectMessage = {
  templateId: 'task-assignment',
  channels: ['slack'],
  recipients: {
    slack: ['@jane.smith', '@bob.wilson']
  },
  data: {
    taskTitle: 'Review API Documentation',
    assignedBy: 'John Doe',
    dueDate: '2025-06-25',
    priority: 'high',
    taskUrl: 'https://project.example.com/tasks/123'
  }
};</code></pre>
                        </div>

                        <h3>Rich Slack Messages with Blocks</h3>
                        <div class="code-block">
                            <pre><code>const richSlackMessage = {
  templateId: 'incident-alert',
  channels: ['slack'],
  recipients: {
    slack: ['#incidents', '#on-call']
  },
  data: {
    incidentId: 'INC-2025-042',
    severity: 'high',
    title: 'Database Connection Issues',
    description: 'Multiple users reporting login failures',
    affectedServices: ['auth-service', 'user-service'],
    incidentUrl: 'https://status.example.com/incidents/042',
    assignedTo: 'on-call-engineer'
  }
};</code></pre>
                        </div>
                    </section>

                    <section id="hubspot-integration" class="content-section">
                        <h2>HubSpot Integration</h2>

                        <h3>Contact Creation with Notification</h3>
                        <div class="code-block">
                            <pre><code>const hubspotContactNotification = {
  templateId: 'new-lead-alert',
  channels: ['hubspot', 'slack'],
  recipients: {
    hubspot: ['sales-team'],
    slack: ['#sales']
  },
  data: {
    contactEmail: '<EMAIL>',
    contactName: 'Sarah Johnson',
    company: 'Tech Startup Inc',
    leadSource: 'website-form',
    leadScore: 85,
    interests: ['enterprise-plan', 'api-integration']
  }
};</code></pre>
                        </div>

                        <h3>Deal Stage Updates</h3>
                        <div class="code-block">
                            <pre><code>const dealUpdateNotification = {
  templateId: 'deal-stage-change',
  channels: ['hubspot', 'email'],
  recipients: {
    hubspot: ['deal-owners'],
    email: ['<EMAIL>']
  },
  data: {
    dealName: 'Enterprise Contract - Tech Startup Inc',
    previousStage: 'Proposal Sent',
    newStage: 'Negotiation',
    dealValue: '$50,000',
    closeDate: '2025-07-15',
    probability: '75%',
    dealOwner: 'Mike Sales'
  }
};</code></pre>
                        </div>
                    </section>

                    <section id="webhook-integration" class="content-section">
                        <h2>Webhook Integration</h2>

                        <h3>Setting Up Webhooks</h3>
                        <p>
                            Configure webhooks to receive real-time updates
                            about notification delivery status:
                        </p>

                        <div class="code-block">
                            <pre><code>// Webhook endpoint setup (Express.js example)
app.post('/webhooks/notifications', (req, res) => {
  const { event, data } = req.body;

  switch (event) {
    case 'notification.delivered':
      console.log(`Notification ${data.notificationId} delivered to ${data.channel}`);
      // Update your database, send analytics, etc.
      break;

    case 'notification.failed':
      console.log(`Notification ${data.notificationId} failed: ${data.error}`);
      // Handle failure, retry logic, etc.
      break;

    case 'notification.bounced':
      console.log(`Email bounced for ${data.recipient}`);
      // Update contact status, remove from list, etc.
      break;
  }

  res.status(200).send('OK');
});</code></pre>
                        </div>

                        <h3>Webhook Security</h3>
                        <div class="code-block">
                            <pre><code>const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

app.post('/webhooks/notifications', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = JSON.stringify(req.body);

  if (!verifyWebhookSignature(payload, signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).send('Unauthorized');
  }

  // Process webhook...
});</code></pre>
                        </div>
                    </section>

                    <section id="best-practices" class="content-section">
                        <h2>Best Practices</h2>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h3>🔒 Security</h3>
                                <ul>
                                    <li>
                                        Store API keys securely in environment
                                        variables
                                    </li>
                                    <li>Use HTTPS for all API calls</li>
                                    <li>
                                        Implement webhook signature verification
                                    </li>
                                    <li>Rotate API keys regularly</li>
                                    <li>
                                        Use least-privilege access principles
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>⚡ Performance</h3>
                                <ul>
                                    <li>
                                        Use bulk operations for multiple
                                        notifications
                                    </li>
                                    <li>
                                        Implement proper error handling and
                                        retries
                                    </li>
                                    <li>Cache template data when possible</li>
                                    <li>Use appropriate priority levels</li>
                                    <li>Monitor API rate limits</li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>📊 Monitoring</h3>
                                <ul>
                                    <li>Track notification delivery rates</li>
                                    <li>Monitor API response times</li>
                                    <li>
                                        Set up alerts for failed notifications
                                    </li>
                                    <li>Log important events for debugging</li>
                                    <li>
                                        Use webhook events for real-time updates
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>🎯 User Experience</h3>
                                <ul>
                                    <li>
                                        Personalize notifications with user data
                                    </li>
                                    <li>
                                        Respect user preferences and opt-outs
                                    </li>
                                    <li>Use appropriate notification timing</li>
                                    <li>Provide clear unsubscribe options</li>
                                    <li>
                                        Test templates across different devices
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <h3>Error Handling Example</h3>
                        <div class="code-block">
                            <pre><code>async function sendNotificationWithRetry(notificationData, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(`${BASE_URL}/notifications/send`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(notificationData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      console.log(`Attempt ${attempt} failed:`, error.message);

      if (attempt === maxRetries) {
        throw new Error(`Failed to send notification after ${maxRetries} attempts`);
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
}</code></pre>
                        </div>
                    </section>
                </div>

                <!-- Page Navigation -->
                <nav class="page-nav" aria-label="Page navigation">
                    <a
                        href="api-reference.html"
                        class="page-nav-link page-nav-prev"
                    >
                        ← API Reference
                    </a>
                    <a
                        href="../development/development-guide.html"
                        class="page-nav-link page-nav-next"
                    >
                        Development Guide →
                    </a>
                </nav>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer" role="contentinfo">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-title">Integration</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="integration-guide.html"
                                    >Integration Guide</a
                                >
                            </li>
                            <li>
                                <a href="api-reference.html">API Reference</a>
                            </li>
                            <li>
                                <a href="../development/development-guide.html"
                                    >Development Guide</a
                                >
                            </li>
                            <li>
                                <a href="../operations/testing.html"
                                    >Testing Guide</a
                                >
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Quick Links</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="../index.html">Documentation Home</a>
                            </li>
                            <li>
                                <a href="../overview/project-overview.html"
                                    >Project Overview</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../troubleshooting/troubleshooting-guide.html"
                                    >Troubleshooting</a
                                >
                            </li>
                            <li>
                                <a href="../development/contributing.html"
                                    >Contributing</a
                                >
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p class="footer-copyright">
                        © 2025 Notification Engine Documentation. Built with ❤️
                        for Heatmap Inc.
                    </p>
                </div>
            </div>
        </footer>
    </body>
</html>
