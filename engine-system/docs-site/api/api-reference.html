<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta
            name="description"
            content="Notification Engine API Reference - Complete REST API documentation with endpoints, schemas, and examples"
        />
        <title>API Reference - Notification Engine Documentation</title>
        <link rel="stylesheet" href="../assets/styles.css" />
        <link rel="stylesheet" href="../assets/doc-page.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <!-- Skip to main content for accessibility -->
        <a href="#main-content" class="skip-link">Skip to main content</a>

        <!-- Header Navigation -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <a href="../index.html" class="logo-link">
                            <h1 class="logo-text">
                                <span class="logo-icon">🔔</span>
                                Notification Engine
                            </h1>
                            <p class="logo-subtitle">Documentation Hub</p>
                        </a>
                    </div>

                    <nav
                        class="main-nav"
                        role="navigation"
                        aria-label="Main navigation"
                    >
                        <ul class="nav-list">
                            <li>
                                <a
                                    href="../index.html#getting-started"
                                    class="nav-link"
                                    >Getting Started</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#api-integration"
                                    class="nav-link"
                                    >API & Integration</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#architecture"
                                    class="nav-link"
                                    >Architecture</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#operations"
                                    class="nav-link"
                                    >Operations</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#development"
                                    class="nav-link"
                                    >Development</a
                                >
                            </li>
                        </ul>
                    </nav>

                    <div class="header-actions">
                        <a href="../index.html" class="btn btn-secondary"
                            >← Back to Home</a
                        >
                        <a href="integration-guide.html" class="btn btn-primary"
                            >Integration Guide</a
                        >
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="main-content" class="main-content" role="main">
            <div class="container">
                <!-- Page Header -->
                <div class="page-header">
                    <nav class="breadcrumb" aria-label="Breadcrumb">
                        <ol class="breadcrumb-list">
                            <li><a href="../index.html">Home</a></li>
                            <li>
                                <a href="../index.html#api-integration"
                                    >API & Integration</a
                                >
                            </li>
                            <li aria-current="page">API Reference</li>
                        </ol>
                    </nav>

                    <h1 class="page-title">API Reference</h1>
                    <p class="page-description">
                        Complete REST API documentation with endpoints,
                        request/response schemas, and practical examples
                    </p>
                </div>

                <!-- Table of Contents -->
                <div class="toc-container">
                    <h2 class="toc-title">API Endpoints</h2>
                    <nav class="toc" aria-label="Table of contents">
                        <ul class="toc-list">
                            <li>
                                <a href="#authentication">Authentication</a>
                            </li>
                            <li><a href="#notifications">Notifications</a></li>
                            <li><a href="#templates">Templates</a></li>
                            <li><a href="#triggers">Triggers</a></li>
                            <li><a href="#health">Health & Status</a></li>
                            <li>
                                <a href="#error-handling">Error Handling</a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- Content Sections -->
                <div class="content">
                    <section id="authentication" class="content-section">
                        <h2>Authentication</h2>
                        <p>
                            The Notification Engine API uses API key
                            authentication. Include your API key in the
                            <code>Authorization</code> header with the
                            <code>Bearer</code> scheme.
                        </p>

                        <div class="code-block">
                            <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                        </div>

                        <div class="highlight-box">
                            <h3>Base URL</h3>
                            <p>
                                <strong>Production:</strong>
                                <code
                                    >https://api.notification-engine.com/v1</code
                                >
                            </p>
                            <p>
                                <strong>Development:</strong>
                                <code>http://localhost:3000/api/v1</code>
                            </p>
                        </div>
                    </section>

                    <section id="notifications" class="content-section">
                        <h2>Notifications</h2>

                        <h3>Send Real-time Notification</h3>
                        <p>
                            Send an immediate notification through specified
                            channels.
                        </p>

                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/notifications/send</span>
                            </div>
                        </div>

                        <h4>Request Body</h4>
                        <div class="code-block">
                            <pre><code>{
  "templateId": "welcome-email",
  "channels": ["email", "slack"],
  "recipients": {
    "email": ["<EMAIL>"],
    "slack": ["#general", "@john.doe"]
  },
  "data": {
    "userName": "John Doe",
    "companyName": "Acme Corp",
    "loginUrl": "https://app.example.com/login"
  },
  "priority": "high",
  "metadata": {
    "source": "user-registration",
    "userId": "12345"
  }
}</code></pre>
                        </div>

                        <h4>Response</h4>
                        <div class="code-block">
                            <pre><code>{
  "success": true,
  "notificationId": "notif_abc123",
  "status": "queued",
  "channels": {
    "email": {
      "status": "queued",
      "jobId": "email_job_456"
    },
    "slack": {
      "status": "queued",
      "jobId": "slack_job_789"
    }
  },
  "estimatedDelivery": "2025-06-20T10:30:00Z"
}</code></pre>
                        </div>

                        <h3>Schedule Notification</h3>
                        <p>Schedule a notification for future delivery.</p>

                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path"
                                    >/notifications/schedule</span
                                >
                            </div>
                        </div>

                        <h4>Request Body</h4>
                        <div class="code-block">
                            <pre><code>{
  "templateId": "reminder-email",
  "channels": ["email"],
  "recipients": {
    "email": ["<EMAIL>"]
  },
  "data": {
    "eventName": "Team Meeting",
    "eventDate": "2025-06-25T14:00:00Z"
  },
  "scheduledFor": "2025-06-25T13:45:00Z",
  "timezone": "America/New_York"
}</code></pre>
                        </div>

                        <h3>Get Notification Status</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path"
                                    >/notifications/{notificationId}</span
                                >
                            </div>
                        </div>

                        <h4>Response</h4>
                        <div class="code-block">
                            <pre><code>{
  "id": "notif_abc123",
  "status": "delivered",
  "templateId": "welcome-email",
  "channels": {
    "email": {
      "status": "delivered",
      "deliveredAt": "2025-06-20T10:32:15Z",
      "recipient": "<EMAIL>"
    },
    "slack": {
      "status": "delivered",
      "deliveredAt": "2025-06-20T10:32:08Z",
      "channel": "#general"
    }
  },
  "createdAt": "2025-06-20T10:30:00Z",
  "metadata": {
    "source": "user-registration",
    "userId": "12345"
  }
}</code></pre>
                        </div>
                    </section>

                    <section id="templates" class="content-section">
                        <h2>Templates</h2>

                        <h3>List Templates</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/templates</span>
                            </div>
                        </div>

                        <h4>Query Parameters</h4>
                        <ul>
                            <li>
                                <code>channel</code> - Filter by channel (email,
                                slack, hubspot)
                            </li>
                            <li>
                                <code>category</code> - Filter by template
                                category
                            </li>
                            <li>
                                <code>active</code> - Filter by active status
                                (true/false)
                            </li>
                            <li>
                                <code>limit</code> - Number of results (default:
                                50)
                            </li>
                            <li>
                                <code>offset</code> - Pagination offset
                                (default: 0)
                            </li>
                        </ul>

                        <h3>Get Template</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path"
                                    >/templates/{templateId}</span
                                >
                            </div>
                        </div>

                        <h4>Response</h4>
                        <div class="code-block">
                            <pre><code>{
  "id": "welcome-email",
  "name": "Welcome Email",
  "description": "Welcome email for new users",
  "channels": {
    "email": {
      "subject": "Welcome to {{companyName}}!",
      "htmlTemplate": "&lt;h1&gt;Welcome {{userName}}!&lt;/h1&gt;...",
      "textTemplate": "Welcome {{userName}}!..."
    }
  },
  "schema": {
    "type": "object",
    "required": ["userName", "companyName"],
    "properties": {
      "userName": {"type": "string"},
      "companyName": {"type": "string"},
      "loginUrl": {"type": "string", "format": "uri"}
    }
  },
  "active": true,
  "createdAt": "2025-06-01T00:00:00Z",
  "updatedAt": "2025-06-15T12:00:00Z"
}</code></pre>
                        </div>

                        <h3>Create Template</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/templates</span>
                            </div>
                        </div>

                        <h3>Update Template</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method put">PUT</span>
                                <span class="path"
                                    >/templates/{templateId}</span
                                >
                            </div>
                        </div>

                        <h3>Delete Template</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method delete">DELETE</span>
                                <span class="path"
                                    >/templates/{templateId}</span
                                >
                            </div>
                        </div>
                    </section>

                    <section id="triggers" class="content-section">
                        <h2>Triggers</h2>

                        <h3>List Triggers</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/triggers</span>
                            </div>
                        </div>

                        <h3>Create Trigger</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/triggers</span>
                            </div>
                        </div>

                        <h4>Request Body</h4>
                        <div class="code-block">
                            <pre><code>{
  "name": "User Registration Trigger",
  "event": "user.registered",
  "conditions": {
    "userType": "premium",
    "source": "web"
  },
  "templateId": "welcome-email",
  "channels": ["email", "slack"],
  "delay": "PT5M",
  "active": true
}</code></pre>
                        </div>

                        <h3>Trigger Evaluation</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/triggers/evaluate</span>
                            </div>
                        </div>

                        <h4>Request Body</h4>
                        <div class="code-block">
                            <pre><code>{
  "event": "user.registered",
  "data": {
    "userId": "12345",
    "userType": "premium",
    "email": "<EMAIL>",
    "source": "web",
    "registrationDate": "2025-06-20T10:00:00Z"
  }
}</code></pre>
                        </div>
                    </section>

                    <section id="health" class="content-section">
                        <h2>Health & Status</h2>

                        <h3>Health Check</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/health</span>
                            </div>
                        </div>

                        <h4>Response</h4>
                        <div class="code-block">
                            <pre><code>{
  "status": "healthy",
  "timestamp": "2025-06-20T10:30:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "rabbitmq": "healthy",
    "email": "healthy",
    "slack": "healthy"
  },
  "version": "1.0.0"
}</code></pre>
                        </div>

                        <h3>Queue Status</h3>
                        <div class="api-endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/status/queues</span>
                            </div>
                        </div>

                        <h4>Response</h4>
                        <div class="code-block">
                            <pre><code>{
  "queues": {
    "notifications": {
      "waiting": 5,
      "active": 2,
      "completed": 1250,
      "failed": 3
    },
    "triggers": {
      "waiting": 0,
      "active": 1,
      "completed": 890,
      "failed": 1
    }
  },
  "workers": {
    "notification-worker": {
      "status": "active",
      "processed": 1250,
      "failed": 3
    }
  }
}</code></pre>
                        </div>
                    </section>

                    <section id="error-handling" class="content-section">
                        <h2>Error Handling</h2>

                        <p>
                            The API uses conventional HTTP response codes to
                            indicate success or failure:
                        </p>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h3>Success Codes</h3>
                                <ul>
                                    <li>
                                        <strong>200 OK</strong> - Request
                                        successful
                                    </li>
                                    <li>
                                        <strong>201 Created</strong> - Resource
                                        created successfully
                                    </li>
                                    <li>
                                        <strong>204 No Content</strong> -
                                        Request successful, no content returned
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>Client Error Codes</h3>
                                <ul>
                                    <li>
                                        <strong>400 Bad Request</strong> -
                                        Invalid request parameters
                                    </li>
                                    <li>
                                        <strong>401 Unauthorized</strong> -
                                        Invalid or missing API key
                                    </li>
                                    <li>
                                        <strong>403 Forbidden</strong> -
                                        Insufficient permissions
                                    </li>
                                    <li>
                                        <strong>404 Not Found</strong> -
                                        Resource not found
                                    </li>
                                    <li>
                                        <strong
                                            >422 Unprocessable Entity</strong
                                        >
                                        - Validation errors
                                    </li>
                                    <li>
                                        <strong>429 Too Many Requests</strong> -
                                        Rate limit exceeded
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>Server Error Codes</h3>
                                <ul>
                                    <li>
                                        <strong
                                            >500 Internal Server Error</strong
                                        >
                                        - Server error
                                    </li>
                                    <li>
                                        <strong>502 Bad Gateway</strong> -
                                        Upstream service error
                                    </li>
                                    <li>
                                        <strong>503 Service Unavailable</strong>
                                        - Service temporarily unavailable
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <h3>Error Response Format</h3>
                        <div class="code-block">
                            <pre><code>{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": [
      {
        "field": "templateId",
        "message": "Template ID is required"
      },
      {
        "field": "recipients.email",
        "message": "At least one email recipient is required"
      }
    ],
    "requestId": "req_abc123"
  }
}</code></pre>
                        </div>
                    </section>
                </div>

                <!-- Page Navigation -->
                <nav class="page-nav" aria-label="Page navigation">
                    <a
                        href="../overview/project-overview.html"
                        class="page-nav-link page-nav-prev"
                    >
                        ← Project Overview
                    </a>
                    <a
                        href="integration-guide.html"
                        class="page-nav-link page-nav-next"
                    >
                        Integration Guide →
                    </a>
                </nav>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer" role="contentinfo">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-title">API Documentation</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="api-reference.html">API Reference</a>
                            </li>
                            <li>
                                <a href="integration-guide.html"
                                    >Integration Guide</a
                                >
                            </li>
                            <li>
                                <a href="../development/development-guide.html"
                                    >Development Guide</a
                                >
                            </li>
                            <li>
                                <a href="../operations/testing.html"
                                    >Testing Guide</a
                                >
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Quick Links</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="../index.html">Documentation Home</a>
                            </li>
                            <li>
                                <a href="../overview/project-overview.html"
                                    >Project Overview</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../troubleshooting/troubleshooting-guide.html"
                                    >Troubleshooting</a
                                >
                            </li>
                            <li>
                                <a href="../development/contributing.html"
                                    >Contributing</a
                                >
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p class="footer-copyright">
                        © 2025 Notification Engine Documentation. Built with ❤️
                        for Heatmap Inc.
                    </p>
                </div>
            </div>
        </footer>
    </body>
</html>
