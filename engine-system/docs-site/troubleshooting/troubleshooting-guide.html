<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Troubleshooting Guide - Common issues, debugging techniques, and solutions">
    <title>Troubleshooting Guide - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/styles.css">
    <link rel="stylesheet" href="../assets/doc-page.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html" class="logo-link">
                        <h1 class="logo-text">
                            <span class="logo-icon">🔔</span>
                            Notification Engine
                        </h1>
                        <p class="logo-subtitle">Documentation Hub</p>
                    </a>
                </div>
                
                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="../index.html#getting-started" class="nav-link">Getting Started</a></li>
                        <li><a href="../index.html#api-integration" class="nav-link">API & Integration</a></li>
                        <li><a href="../index.html#architecture" class="nav-link">Architecture</a></li>
                        <li><a href="../index.html#operations" class="nav-link">Operations</a></li>
                        <li><a href="../index.html#development" class="nav-link">Development</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <a href="../index.html" class="btn btn-secondary">← Back to Home</a>
                    <a href="../api/api-reference.html" class="btn btn-primary">API Reference</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../index.html#operations">Operations</a></li>
                        <li aria-current="page">Troubleshooting Guide</li>
                    </ol>
                </nav>
                
                <h1 class="page-title">Troubleshooting Guide</h1>
                <p class="page-description">
                    Common issues, debugging techniques, and solutions for the Notification Engine
                </p>
            </div>

            <!-- Table of Contents -->
            <div class="toc-container">
                <h2 class="toc-title">Troubleshooting Topics</h2>
                <nav class="toc" aria-label="Table of contents">
                    <ul class="toc-list">
                        <li><a href="#common-issues">Common Issues</a></li>
                        <li><a href="#database-issues">Database Issues</a></li>
                        <li><a href="#queue-issues">Queue System Issues</a></li>
                        <li><a href="#email-issues">Email Delivery Issues</a></li>
                        <li><a href="#slack-issues">Slack Integration Issues</a></li>
                        <li><a href="#performance-issues">Performance Issues</a></li>
                        <li><a href="#debugging-tools">Debugging Tools</a></li>
                    </ul>
                </nav>
            </div>

            <!-- Content Sections -->
            <div class="content">
                <section id="common-issues" class="content-section">
                    <h2>Common Issues</h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>🚫 API Authentication Errors</h3>
                            <p><strong>Symptoms:</strong> 401 Unauthorized responses</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Verify API key is correct and active</li>
                                <li>Check Authorization header format: <code>Bearer YOUR_API_KEY</code></li>
                                <li>Ensure API key has required permissions</li>
                                <li>Check for expired or revoked keys</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>📧 Notifications Not Sending</h3>
                            <p><strong>Symptoms:</strong> Notifications stuck in queue or failing</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Check template exists and is active</li>
                                <li>Verify recipient format is correct</li>
                                <li>Check queue worker status</li>
                                <li>Review error logs for specific failures</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>🐌 Slow API Responses</h3>
                            <p><strong>Symptoms:</strong> High response times, timeouts</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Check database connection pool</li>
                                <li>Monitor Redis performance</li>
                                <li>Review queue backlog</li>
                                <li>Check server resource usage</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>🔄 Template Rendering Errors</h3>
                            <p><strong>Symptoms:</strong> Malformed notifications, missing data</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Validate template syntax</li>
                                <li>Check data schema matches template</li>
                                <li>Review Handlebars helper functions</li>
                                <li>Test template with sample data</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section id="database-issues" class="content-section">
                    <h2>Database Issues</h2>
                    
                    <h3>Connection Problems</h3>
                    <div class="code-block">
                        <pre><code># Check database connectivity
mysql -h maindb.cfo2fkycjeqt.us-west-2.rds.amazonaws.com -u notification -p notification_engine

# Test connection from application
npm run db:test-connection</code></pre>
                    </div>

                    <h3>Common Database Errors</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>Connection Timeout</h3>
                            <p><strong>Error:</strong> <code>ETIMEDOUT</code> or <code>Connection lost</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Check network connectivity to RDS instance</li>
                                <li>Verify security group allows connections</li>
                                <li>Increase connection timeout in Prisma config</li>
                                <li>Check RDS instance status</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Too Many Connections</h3>
                            <p><strong>Error:</strong> <code>Too many connections</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Reduce connection pool size</li>
                                <li>Check for connection leaks</li>
                                <li>Monitor active connections</li>
                                <li>Scale RDS instance if needed</li>
                            </ul>
                        </div>
                    </div>

                    <h3>Database Monitoring</h3>
                    <div class="code-block">
                        <pre><code># Check active connections
SHOW PROCESSLIST;

# Monitor connection pool
SELECT * FROM information_schema.PROCESSLIST 
WHERE USER = 'notification';

# Check database size
SELECT 
  table_schema AS 'Database',
  ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'notification_engine';</code></pre>
                    </div>
                </section>

                <section id="queue-issues" class="content-section">
                    <h2>Queue System Issues</h2>
                    
                    <h3>Queue Monitoring</h3>
                    <p>Access the Bull Board dashboard at <code>http://localhost:3000/admin/queues</code> to monitor queue status.</p>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>Jobs Stuck in Queue</h3>
                            <p><strong>Symptoms:</strong> Jobs not processing, growing backlog</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Check worker processes are running</li>
                                <li>Verify Redis connectivity</li>
                                <li>Review job error logs</li>
                                <li>Restart queue workers</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>High Job Failure Rate</h3>
                            <p><strong>Symptoms:</strong> Many jobs in failed state</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Check external service availability</li>
                                <li>Review job retry configuration</li>
                                <li>Validate job data format</li>
                                <li>Check rate limiting settings</li>
                            </ul>
                        </div>
                    </div>

                    <h3>Queue Commands</h3>
                    <div class="code-block">
                        <pre><code># Check Redis connection
redis-cli ping

# Monitor queue stats
redis-cli info

# List all queues
redis-cli keys "bull:*"

# Check specific queue
redis-cli llen "bull:notifications:waiting"

# Clear failed jobs
npm run queue:clear-failed</code></pre>
                    </div>
                </section>

                <section id="email-issues" class="content-section">
                    <h2>Email Delivery Issues</h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>SMTP Connection Errors</h3>
                            <p><strong>Error:</strong> <code>ECONNREFUSED</code> or <code>Authentication failed</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Verify SMTP credentials in .env file</li>
                                <li>Check SMTP server hostname and port</li>
                                <li>Test SMTP connection manually</li>
                                <li>Check firewall/security group settings</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Emails Going to Spam</h3>
                            <p><strong>Symptoms:</strong> Low delivery rates, spam complaints</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Configure SPF, DKIM, and DMARC records</li>
                                <li>Use authenticated SMTP service</li>
                                <li>Review email content and formatting</li>
                                <li>Monitor sender reputation</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Template Rendering Issues</h3>
                            <p><strong>Symptoms:</strong> Broken HTML, missing images</p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Validate HTML template syntax</li>
                                <li>Check image URLs are accessible</li>
                                <li>Test templates in email clients</li>
                                <li>Use absolute URLs for assets</li>
                            </ul>
                        </div>
                    </div>

                    <h3>Email Testing</h3>
                    <div class="code-block">
                        <pre><code># Test SMTP connection
npm run test:smtp

# Send test email
curl -X POST http://localhost:3000/api/v1/notifications/send \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "test-email",
    "channels": ["email"],
    "recipients": {"email": ["<EMAIL>"]},
    "data": {"testMessage": "Hello World"}
  }'</code></pre>
                    </div>
                </section>

                <section id="slack-issues" class="content-section">
                    <h2>Slack Integration Issues</h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>Bot Token Issues</h3>
                            <p><strong>Error:</strong> <code>invalid_auth</code> or <code>token_revoked</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Verify bot token is correct and active</li>
                                <li>Check bot permissions and scopes</li>
                                <li>Ensure bot is added to target channels</li>
                                <li>Regenerate token if compromised</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Channel Not Found</h3>
                            <p><strong>Error:</strong> <code>channel_not_found</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Verify channel name format (#channel-name)</li>
                                <li>Check bot has access to channel</li>
                                <li>Invite bot to private channels</li>
                                <li>Use channel ID instead of name</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Rate Limiting</h3>
                            <p><strong>Error:</strong> <code>rate_limited</code></p>
                            <p><strong>Solutions:</strong></p>
                            <ul>
                                <li>Implement exponential backoff</li>
                                <li>Reduce message frequency</li>
                                <li>Use batch operations where possible</li>
                                <li>Monitor API usage</li>
                            </ul>
                        </div>
                    </div>

                    <h3>Slack Testing</h3>
                    <div class="code-block">
                        <pre><code># Test Slack connection
npm run test:slack

# List channels bot has access to
curl -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
  https://slack.com/api/conversations.list

# Send test message
curl -X POST http://localhost:3000/api/v1/notifications/send \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "test-slack",
    "channels": ["slack"],
    "recipients": {"slack": ["#test-channel"]},
    "data": {"message": "Test notification"}
  }'</code></pre>
                    </div>
                </section>

                <section id="performance-issues" class="content-section">
                    <h2>Performance Issues</h2>
                    
                    <h3>Monitoring Performance</h3>
                    <div class="code-block">
                        <pre><code># Check system resources
top
htop
free -h
df -h

# Monitor Node.js process
npm run monitor

# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/api/v1/health</code></pre>
                    </div>

                    <h3>Performance Optimization</h3>
                    <ul>
                        <li><strong>Database:</strong> Add indexes, optimize queries, use connection pooling</li>
                        <li><strong>Redis:</strong> Monitor memory usage, configure eviction policies</li>
                        <li><strong>Queue:</strong> Adjust concurrency settings, optimize job processing</li>
                        <li><strong>API:</strong> Implement caching, use compression, optimize middleware</li>
                    </ul>
                </section>

                <section id="debugging-tools" class="content-section">
                    <h2>Debugging Tools</h2>
                    
                    <h3>Logging</h3>
                    <div class="code-block">
                        <pre><code># View application logs
tail -f logs/engine-system/app.log

# Filter by log level
grep "ERROR" logs/engine-system/app.log

# View queue logs
tail -f logs/engine-system/queue.log

# Real-time log monitoring
npm run logs:watch</code></pre>
                    </div>

                    <h3>Health Checks</h3>
                    <div class="code-block">
                        <pre><code># System health check
curl http://localhost:3000/api/v1/health

# Database health
curl http://localhost:3000/api/v1/health/database

# Queue health
curl http://localhost:3000/api/v1/health/queues

# External services health
curl http://localhost:3000/api/v1/health/services</code></pre>
                    </div>

                    <h3>Useful Commands</h3>
                    <div class="code-block">
                        <pre><code># Check environment variables
npm run env:check

# Validate configuration
npm run config:validate

# Test all integrations
npm run test:integrations

# Generate diagnostic report
npm run diagnostics</code></pre>
                    </div>

                    <div class="highlight-box">
                        <h3>Getting Help</h3>
                        <p>If you're still experiencing issues after trying these solutions:</p>
                        <ul>
                            <li>Check the <a href="../api/api-reference.html">API Reference</a> for correct usage</li>
                            <li>Review the <a href="../development/development-guide.html">Development Guide</a> for setup instructions</li>
                            <li>Search existing issues in the repository</li>
                            <li>Create a new issue with detailed error logs and reproduction steps</li>
                            <li>Contact the development team with diagnostic information</li>
                        </ul>
                    </div>
                </section>
            </div>

            <!-- Page Navigation -->
            <nav class="page-nav" aria-label="Page navigation">
                <a href="../development/development-guide.html" class="page-nav-link page-nav-prev">
                    ← Development Guide
                </a>
                <a href="../index.html" class="page-nav-link page-nav-next">
                    Back to Home →
                </a>
            </nav>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="troubleshooting-guide.html">Troubleshooting Guide</a></li>
                        <li><a href="../api/api-reference.html">API Reference</a></li>
                        <li><a href="../development/development-guide.html">Development Guide</a></li>
                        <li><a href="../development/contributing.html">Contributing</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="../index.html">Documentation Home</a></li>
                        <li><a href="../overview/project-overview.html">Project Overview</a></li>
                        <li><a href="../api/integration-guide.html">Integration Guide</a></li>
                        <li><a href="../operations/deployment.html">Deployment</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Built with ❤️ for developers.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
