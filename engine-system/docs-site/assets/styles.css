/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #1a202c;
    background-color: #ffffff;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid #3182ce;
    outline-offset: 2px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
    color: #1a202c;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    color: #4a5568;
}

a {
    color: #3182ce;
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: #2c5aa0;
    text-decoration: underline;
}

/* ===== LAYOUT UTILITIES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: 2px solid transparent;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    white-space: nowrap;
}

.btn-primary {
    background-color: #3182ce;
    color: white;
    border-color: #3182ce;
}

.btn-primary:hover {
    background-color: #2c5aa0;
    border-color: #2c5aa0;
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
    border-color: #e2e8f0;
}

.btn-secondary:hover {
    background-color: #cbd5e0;
    border-color: #cbd5e0;
    color: #2d3748;
    text-decoration: none;
}

.btn-outline {
    background-color: transparent;
    color: #3182ce;
    border-color: #3182ce;
}

.btn-outline:hover {
    background-color: #3182ce;
    color: white;
    text-decoration: none;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* ===== HEADER ===== */
.header {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    gap: 2rem;
}

.logo {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.logo-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
}

.logo-icon {
    font-size: 1.75rem;
}

.logo-subtitle {
    font-size: 0.875rem;
    color: #718096;
    margin: 0;
}

.main-nav {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    font-weight: 500;
    color: #4a5568;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: #3182ce;
    text-decoration: none;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.7;
}

.hero-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 500;
}

.feature-icon {
    font-size: 1.25rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ===== ARCHITECTURE DIAGRAM ===== */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.architecture-diagram {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.diagram-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: white;
}

.diagram-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.diagram-layer {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.diagram-box {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    min-width: 100px;
    backdrop-filter: blur(5px);
}

/* ===== DOCUMENTATION SECTIONS ===== */
.documentation-sections {
    padding: 4rem 0;
    background-color: #f7fafc;
}

.doc-section {
    margin-bottom: 4rem;
}

.doc-section:last-child {
    margin-bottom: 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a202c;
}

.section-icon {
    font-size: 2rem;
}

.section-description {
    font-size: 1.125rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
}

.doc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* ===== DOCUMENTATION CARDS ===== */
.doc-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.doc-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.doc-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.doc-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.doc-card-badge {
    background-color: #e2e8f0;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.doc-card-description {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.doc-card-footer {
    margin-top: auto;
}

.doc-card-link {
    color: #3182ce;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.doc-card-link:hover {
    color: #2c5aa0;
    text-decoration: underline;
}

/* ===== QUICK LINKS ===== */
.quick-links {
    padding: 3rem 0;
    background-color: white;
    border-top: 1px solid #e2e8f0;
}

.quick-links-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.quick-links-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1a202c;
}

.quick-links-description {
    color: #4a5568;
    font-size: 1.125rem;
}

.quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quick-link-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
}

.quick-link-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
    text-decoration: none;
}

.quick-link-icon {
    font-size: 1.5rem;
}

.quick-link-text {
    font-weight: 500;
    color: #1a202c;
}

/* ===== FOOTER ===== */
.footer {
    background-color: #1a202c;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #a0aec0;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: white;
    text-decoration: underline;
}

.footer-text {
    color: #a0aec0;
    line-height: 1.6;
}

.footer-bottom {
    border-top: 1px solid #2d3748;
    padding-top: 1rem;
    text-align: center;
}

.footer-copyright {
    color: #a0aec0;
    font-size: 0.875rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-nav {
        order: 3;
        width: 100%;
    }
    
    .nav-list {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .header-actions {
        order: 2;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.25rem;
    }
    
    .hero-features {
        grid-template-columns: 1fr;
    }
    
    .doc-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-links-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 1.875rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }
    
    .btn-large {
        padding: 0.875rem 1.75rem;
        font-size: 0.9375rem;
    }
}
