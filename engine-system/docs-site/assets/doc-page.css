/* ===== DOCUMENTATION PAGE STYLES ===== */

/* Logo link styling for doc pages */
.logo-link {
    text-decoration: none;
    color: inherit;
}

.logo-link:hover {
    text-decoration: none;
    color: inherit;
}

/* Page Header */
.page-header {
    padding: 2rem 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.breadcrumb {
    margin-bottom: 1rem;
}

.breadcrumb-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
    align-items: center;
}

.breadcrumb-list li {
    display: flex;
    align-items: center;
}

.breadcrumb-list li:not(:last-child)::after {
    content: '→';
    margin-left: 0.5rem;
    color: #a0aec0;
}

.breadcrumb-list a {
    color: #3182ce;
    text-decoration: none;
    font-size: 0.875rem;
}

.breadcrumb-list a:hover {
    text-decoration: underline;
}

.breadcrumb-list li[aria-current="page"] {
    color: #4a5568;
    font-size: 0.875rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a202c;
}

.page-description {
    font-size: 1.25rem;
    color: #4a5568;
    line-height: 1.6;
    max-width: 800px;
}

/* Table of Contents */
.toc-container {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 3rem;
}

.toc-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a202c;
}

.toc-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
}

.toc-list a {
    color: #3182ce;
    text-decoration: none;
    padding: 0.5rem 0;
    display: block;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toc-list a:hover {
    background: #e2e8f0;
    text-decoration: none;
    padding-left: 0.5rem;
}

/* Content Sections */
.content {
    max-width: 900px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    scroll-margin-top: 100px; /* Account for sticky header */
}

.content-section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1a202c;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.content-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: #1a202c;
}

.content-section h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
    color: #1a202c;
}

.content-section p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.content-section ul, .content-section ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.content-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Highlight Boxes */
.highlight-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
}

.highlight-box h3 {
    color: white;
    margin-bottom: 1rem;
}

.highlight-box ul {
    margin: 0;
}

.highlight-box li {
    margin-bottom: 0.75rem;
}

.highlight-box strong {
    color: rgba(255, 255, 255, 0.95);
}

/* Architecture Overview */
.architecture-overview {
    margin: 2rem 0;
}

.arch-diagram {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin: 1.5rem 0;
}

.arch-layer {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.arch-layer:last-child {
    margin-bottom: 0;
}

.arch-component {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.arch-component h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a202c;
}

.arch-component ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.arch-component li {
    padding: 0.25rem 0;
    color: #4a5568;
    font-size: 0.875rem;
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.feature-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a202c;
}

.feature-card ul {
    margin: 0;
    padding-left: 1.25rem;
}

.feature-card li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 0.9375rem;
}

/* Technology Grid */
.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.tech-category {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
}

.tech-category h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a202c;
}

.tech-category ul {
    margin: 0;
    padding-left: 1.25rem;
}

.tech-category li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 0.9375rem;
}

/* Code Blocks */
.code-block {
    background: #1a202c;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block code {
    color: #e2e8f0;
    font-family: inherit;
}

/* Next Steps Grid */
.next-steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.next-step-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.next-step-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -3px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.next-step-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1a202c;
}

.next-step-card p {
    color: #4a5568;
    font-size: 0.9375rem;
    line-height: 1.5;
    margin: 0;
}

/* Page Navigation */
.page-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 1px solid #e2e8f0;
}

.page-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-decoration: none;
    color: #3182ce;
    font-weight: 500;
    transition: all 0.2s ease;
}

.page-nav-link:hover {
    background: #edf2f7;
    text-decoration: none;
    transform: translateY(-1px);
}

.page-nav-prev {
    margin-right: auto;
}

.page-nav-next {
    margin-left: auto;
}

/* API Endpoint Styling */
.api-endpoint {
    margin: 1.5rem 0;
}

.endpoint-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.method {
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.method.get {
    background: #c6f6d5;
    color: #22543d;
}

.method.post {
    background: #bee3f8;
    color: #1a365d;
}

.method.put {
    background: #fbb6ce;
    color: #702459;
}

.method.delete {
    background: #fed7d7;
    color: #742a2a;
}

.path {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 1rem;
    font-weight: 500;
    color: #1a202c;
}

/* Responsive Design for Doc Pages */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .page-description {
        font-size: 1.125rem;
    }

    .toc-list {
        grid-template-columns: 1fr;
    }

    .arch-layer {
        grid-template-columns: 1fr;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .next-steps-grid {
        grid-template-columns: 1fr;
    }

    .page-nav {
        flex-direction: column;
        gap: 1rem;
    }

    .page-nav-link {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1rem 0;
    }

    .page-title {
        font-size: 1.75rem;
    }

    .toc-container {
        padding: 1rem;
    }

    .arch-diagram {
        padding: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .code-block {
        padding: 1rem;
    }
}
