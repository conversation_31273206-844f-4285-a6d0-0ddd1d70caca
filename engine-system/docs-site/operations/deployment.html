<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Deployment Guide - Production deployment instructions and configuration">
    <title>Deployment Guide - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/styles.css">
    <link rel="stylesheet" href="../assets/doc-page.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html" class="logo-link">
                        <h1 class="logo-text">
                            <span class="logo-icon">🔔</span>
                            Notification Engine
                        </h1>
                        <p class="logo-subtitle">Documentation Hub</p>
                    </a>
                </div>
                
                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="../index.html#getting-started" class="nav-link">Getting Started</a></li>
                        <li><a href="../index.html#api-integration" class="nav-link">API & Integration</a></li>
                        <li><a href="../index.html#architecture" class="nav-link">Architecture</a></li>
                        <li><a href="../index.html#operations" class="nav-link">Operations</a></li>
                        <li><a href="../index.html#development" class="nav-link">Development</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <a href="../index.html" class="btn btn-secondary">← Back to Home</a>
                    <a href="../api/api-reference.html" class="btn btn-primary">API Reference</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../index.html#operations">Operations</a></li>
                        <li aria-current="page">Deployment Guide</li>
                    </ol>
                </nav>
                
                <h1 class="page-title">Deployment Guide</h1>
                <p class="page-description">
                    Production deployment instructions, Docker setup, and environment configuration
                </p>
            </div>

            <!-- Table of Contents -->
            <div class="toc-container">
                <h2 class="toc-title">Deployment Topics</h2>
                <nav class="toc" aria-label="Table of contents">
                    <ul class="toc-list">
                        <li><a href="#prerequisites">Prerequisites</a></li>
                        <li><a href="#docker-deployment">Docker Deployment</a></li>
                        <li><a href="#environment-config">Environment Configuration</a></li>
                        <li><a href="#database-setup">Database Setup</a></li>
                        <li><a href="#monitoring">Monitoring & Health Checks</a></li>
                        <li><a href="#scaling">Scaling Considerations</a></li>
                    </ul>
                </nav>
            </div>

            <!-- Content Sections -->
            <div class="content">
                <section id="prerequisites" class="content-section">
                    <h2>Prerequisites</h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>Infrastructure Requirements</h3>
                            <ul>
                                <li><strong>Server:</strong> 2+ CPU cores, 4GB+ RAM</li>
                                <li><strong>Database:</strong> MySQL 8.0+ (RDS recommended)</li>
                                <li><strong>Cache:</strong> Redis 6.0+ (ElastiCache recommended)</li>
                                <li><strong>Message Queue:</strong> RabbitMQ 3.8+ (AmazonMQ recommended)</li>
                                <li><strong>Storage:</strong> 20GB+ SSD storage</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>External Services</h3>
                            <ul>
                                <li><strong>SMTP Service:</strong> SendGrid, Mailgun, or AWS SES</li>
                                <li><strong>Slack App:</strong> Bot token and permissions</li>
                                <li><strong>HubSpot:</strong> API key and permissions</li>
                                <li><strong>Monitoring:</strong> CloudWatch, DataDog, or similar</li>
                                <li><strong>Load Balancer:</strong> ALB or similar (for HA)</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section id="docker-deployment" class="content-section">
                    <h2>Docker Deployment</h2>
                    
                    <h3>Production Docker Compose</h3>
                    <div class="code-block">
                        <pre><code>version: '3.8'

services:
  notification-engine:
    image: notification-engine:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - RABBITMQ_URL=${RABBITMQ_URL}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - notification-engine
    restart: unless-stopped</code></pre>
                    </div>

                    <h3>Building Production Image</h3>
                    <div class="code-block">
                        <pre><code># Build production image
docker build -t notification-engine:latest .

# Tag for registry
docker tag notification-engine:latest your-registry/notification-engine:v1.0.0

# Push to registry
docker push your-registry/notification-engine:v1.0.0</code></pre>
                    </div>

                    <h3>Deployment Commands</h3>
                    <div class="code-block">
                        <pre><code># Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f notification-engine

# Update deployment
docker-compose pull && docker-compose up -d</code></pre>
                    </div>
                </section>

                <section id="environment-config" class="content-section">
                    <h2>Environment Configuration</h2>
                    
                    <h3>Production Environment Variables</h3>
                    <div class="code-block">
                        <pre><code># Application
NODE_ENV=production
PORT=3000
API_KEY=your-secure-api-key

# Database (AWS RDS)
DATABASE_URL=mysql://username:<EMAIL>:3306/notification_engine

# Redis (AWS ElastiCache)
REDIS_URL=redis://your-redis-cluster.cache.amazonaws.com:6379

# RabbitMQ (AWS AmazonMQ)
RABBITMQ_URL=amqps://username:<EMAIL>:5671

# Email Service (SendGrid)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-production-bot-token
SLACK_SIGNING_SECRET=your-signing-secret

# HubSpot Integration
HUBSPOT_API_KEY=your-hubspot-api-key

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
METRICS_PORT=9090</code></pre>
                    </div>

                    <h3>Security Configuration</h3>
                    <div class="code-block">
                        <pre><code># Security Headers
HELMET_ENABLED=true
CORS_ORIGIN=https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Webhook Security
WEBHOOK_SECRET=your-webhook-secret</code></pre>
                    </div>
                </section>

                <section id="database-setup" class="content-section">
                    <h2>Database Setup</h2>
                    
                    <h3>Production Database Migration</h3>
                    <div class="code-block">
                        <pre><code># Generate Prisma client
npx prisma generate

# Deploy database schema
npx prisma db push

# Run migrations (if using migrate)
npx prisma migrate deploy

# Seed production data
npx prisma db seed --environment=production</code></pre>
                    </div>

                    <h3>Database Backup Strategy</h3>
                    <div class="code-block">
                        <pre><code># Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="notification_engine_backup_$DATE.sql"

mysqldump -h maindb.cfo2fkycjeqt.us-west-2.rds.amazonaws.com \
  -u notification -p notification_engine > $BACKUP_FILE

# Upload to S3
aws s3 cp $BACKUP_FILE s3://your-backup-bucket/database/

# Cleanup local file
rm $BACKUP_FILE</code></pre>
                    </div>

                    <h3>Database Performance Tuning</h3>
                    <ul>
                        <li><strong>Connection Pooling:</strong> Configure appropriate pool size (10-20 connections)</li>
                        <li><strong>Indexes:</strong> Ensure proper indexes on frequently queried columns</li>
                        <li><strong>Query Optimization:</strong> Monitor slow queries and optimize</li>
                        <li><strong>Backup Strategy:</strong> Daily automated backups with point-in-time recovery</li>
                    </ul>
                </section>

                <section id="monitoring" class="content-section">
                    <h2>Monitoring & Health Checks</h2>
                    
                    <h3>Health Check Endpoints</h3>
                    <div class="code-block">
                        <pre><code># Application health
GET /api/v1/health

# Database health
GET /api/v1/health/database

# Queue health
GET /api/v1/health/queues

# External services health
GET /api/v1/health/services</code></pre>
                    </div>

                    <h3>Monitoring Setup</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>Application Metrics</h3>
                            <ul>
                                <li>Response times and throughput</li>
                                <li>Error rates and status codes</li>
                                <li>Memory and CPU usage</li>
                                <li>Active connections</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Business Metrics</h3>
                            <ul>
                                <li>Notification delivery rates</li>
                                <li>Queue processing times</li>
                                <li>Template usage statistics</li>
                                <li>Channel performance</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>Infrastructure Metrics</h3>
                            <ul>
                                <li>Database performance</li>
                                <li>Redis memory usage</li>
                                <li>RabbitMQ queue depths</li>
                                <li>Network latency</li>
                            </ul>
                        </div>
                    </div>

                    <h3>Alerting Configuration</h3>
                    <div class="code-block">
                        <pre><code># CloudWatch Alarms (AWS)
aws cloudwatch put-metric-alarm \
  --alarm-name "NotificationEngine-HighErrorRate" \
  --alarm-description "High error rate detected" \
  --metric-name "ErrorRate" \
  --namespace "NotificationEngine" \
  --statistic "Average" \
  --period 300 \
  --threshold 5.0 \
  --comparison-operator "GreaterThanThreshold" \
  --evaluation-periods 2</code></pre>
                    </div>
                </section>

                <section id="scaling" class="content-section">
                    <h2>Scaling Considerations</h2>
                    
                    <h3>Horizontal Scaling</h3>
                    <ul>
                        <li><strong>Load Balancer:</strong> Use ALB or similar to distribute traffic</li>
                        <li><strong>Multiple Instances:</strong> Run multiple application instances</li>
                        <li><strong>Queue Workers:</strong> Scale queue workers independently</li>
                        <li><strong>Database:</strong> Use read replicas for read-heavy workloads</li>
                    </ul>

                    <h3>Vertical Scaling</h3>
                    <ul>
                        <li><strong>CPU:</strong> Increase CPU cores for compute-intensive tasks</li>
                        <li><strong>Memory:</strong> Increase RAM for caching and queue processing</li>
                        <li><strong>Storage:</strong> Use SSD storage for better I/O performance</li>
                        <li><strong>Network:</strong> Ensure adequate network bandwidth</li>
                    </ul>

                    <h3>Auto Scaling Configuration</h3>
                    <div class="code-block">
                        <pre><code># ECS Auto Scaling (AWS)
{
  "serviceName": "notification-engine",
  "scalingPolicies": [
    {
      "policyName": "cpu-scaling",
      "targetValue": 70.0,
      "scaleOutCooldown": 300,
      "scaleInCooldown": 300,
      "metricType": "ECSServiceAverageCPUUtilization"
    }
  ],
  "minCapacity": 2,
  "maxCapacity": 10
}</code></pre>
                    </div>

                    <div class="highlight-box">
                        <h3>Production Checklist</h3>
                        <ul>
                            <li>✅ Environment variables configured</li>
                            <li>✅ Database migrations applied</li>
                            <li>✅ SSL certificates installed</li>
                            <li>✅ Health checks configured</li>
                            <li>✅ Monitoring and alerting setup</li>
                            <li>✅ Backup strategy implemented</li>
                            <li>✅ Security headers enabled</li>
                            <li>✅ Rate limiting configured</li>
                            <li>✅ Log aggregation setup</li>
                            <li>✅ Load testing completed</li>
                        </ul>
                    </div>
                </section>
            </div>

            <!-- Page Navigation -->
            <nav class="page-nav" aria-label="Page navigation">
                <a href="../troubleshooting/troubleshooting-guide.html" class="page-nav-link page-nav-prev">
                    ← Troubleshooting Guide
                </a>
                <a href="../index.html" class="page-nav-link page-nav-next">
                    Back to Home →
                </a>
            </nav>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Operations</h4>
                    <ul class="footer-links">
                        <li><a href="deployment.html">Deployment Guide</a></li>
                        <li><a href="testing.html">Testing Guide</a></li>
                        <li><a href="../troubleshooting/troubleshooting-guide.html">Troubleshooting</a></li>
                        <li><a href="../development/development-guide.html">Development</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="../index.html">Documentation Home</a></li>
                        <li><a href="../overview/project-overview.html">Project Overview</a></li>
                        <li><a href="../api/api-reference.html">API Reference</a></li>
                        <li><a href="../api/integration-guide.html">Integration Guide</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Built with ❤️ for developers.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
