<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta
            name="description"
            content="Notification Engine Development Guide - Setup instructions, coding standards, and development workflow"
        />
        <title>Development Guide - Notification Engine Documentation</title>
        <link rel="stylesheet" href="../assets/styles.css" />
        <link rel="stylesheet" href="../assets/doc-page.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <!-- Skip to main content for accessibility -->
        <a href="#main-content" class="skip-link">Skip to main content</a>

        <!-- Header Navigation -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <a href="../index.html" class="logo-link">
                            <h1 class="logo-text">
                                <span class="logo-icon">🔔</span>
                                Notification Engine
                            </h1>
                            <p class="logo-subtitle">Documentation Hub</p>
                        </a>
                    </div>

                    <nav
                        class="main-nav"
                        role="navigation"
                        aria-label="Main navigation"
                    >
                        <ul class="nav-list">
                            <li>
                                <a
                                    href="../index.html#getting-started"
                                    class="nav-link"
                                    >Getting Started</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#api-integration"
                                    class="nav-link"
                                    >API & Integration</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#architecture"
                                    class="nav-link"
                                    >Architecture</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#operations"
                                    class="nav-link"
                                    >Operations</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../index.html#development"
                                    class="nav-link"
                                    >Development</a
                                >
                            </li>
                        </ul>
                    </nav>

                    <div class="header-actions">
                        <a href="../index.html" class="btn btn-secondary"
                            >← Back to Home</a
                        >
                        <a
                            href="../api/api-reference.html"
                            class="btn btn-primary"
                            >API Reference</a
                        >
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="main-content" class="main-content" role="main">
            <div class="container">
                <!-- Page Header -->
                <div class="page-header">
                    <nav class="breadcrumb" aria-label="Breadcrumb">
                        <ol class="breadcrumb-list">
                            <li><a href="../index.html">Home</a></li>
                            <li>
                                <a href="../index.html#development"
                                    >Development</a
                                >
                            </li>
                            <li aria-current="page">Development Guide</li>
                        </ol>
                    </nav>

                    <h1 class="page-title">Development Guide</h1>
                    <p class="page-description">
                        Complete setup instructions, coding standards, and
                        development workflow for the Notification Engine
                    </p>
                </div>

                <!-- Table of Contents -->
                <div class="toc-container">
                    <h2 class="toc-title">Development Topics</h2>
                    <nav class="toc" aria-label="Table of contents">
                        <ul class="toc-list">
                            <li><a href="#prerequisites">Prerequisites</a></li>
                            <li>
                                <a href="#environment-setup"
                                    >Environment Setup</a
                                >
                            </li>
                            <li>
                                <a href="#project-structure"
                                    >Project Structure</a
                                >
                            </li>
                            <li>
                                <a href="#coding-standards">Coding Standards</a>
                            </li>
                            <li>
                                <a href="#development-workflow"
                                    >Development Workflow</a
                                >
                            </li>
                            <li><a href="#testing">Testing</a></li>
                            <li><a href="#debugging">Debugging</a></li>
                        </ul>
                    </nav>
                </div>

                <!-- Content Sections -->
                <div class="content">
                    <section id="prerequisites" class="content-section">
                        <h2>Prerequisites</h2>
                        <p>
                            Before setting up the development environment,
                            ensure you have the following installed:
                        </p>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h3>Required Software</h3>
                                <ul>
                                    <li>
                                        <strong>Node.js 18+</strong> -
                                        JavaScript runtime
                                    </li>
                                    <li>
                                        <strong>npm or pnpm</strong> - Package
                                        manager
                                    </li>
                                    <li>
                                        <strong>MySQL 8.0+</strong> - Primary
                                        database
                                    </li>
                                    <li>
                                        <strong>Redis 6.0+</strong> - Queue and
                                        caching
                                    </li>
                                    <li>
                                        <strong>RabbitMQ 3.8+</strong> - Message
                                        queuing
                                    </li>
                                    <li>
                                        <strong>Git</strong> - Version control
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>Development Tools</h3>
                                <ul>
                                    <li>
                                        <strong>VS Code</strong> - Recommended
                                        IDE
                                    </li>
                                    <li>
                                        <strong>Docker & Docker Compose</strong>
                                        - Containerization
                                    </li>
                                    <li>
                                        <strong>Postman/Insomnia</strong> - API
                                        testing
                                    </li>
                                    <li>
                                        <strong>MySQL Workbench</strong> -
                                        Database management
                                    </li>
                                    <li>
                                        <strong>Redis CLI</strong> - Redis
                                        management
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>VS Code Extensions</h3>
                                <ul>
                                    <li>
                                        <strong>TypeScript</strong> - Language
                                        support
                                    </li>
                                    <li>
                                        <strong>Prettier</strong> - Code
                                        formatting
                                    </li>
                                    <li>
                                        <strong>ESLint</strong> - Code linting
                                    </li>
                                    <li>
                                        <strong>Prisma</strong> - Database
                                        schema
                                    </li>
                                    <li>
                                        <strong>Thunder Client</strong> - API
                                        testing
                                    </li>
                                    <li>
                                        <strong>GitLens</strong> - Git
                                        integration
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </section>

                    <section id="environment-setup" class="content-section">
                        <h2>Environment Setup</h2>

                        <h3>1. Clone the Repository</h3>
                        <div class="code-block">
                            <pre><code>git clone &lt;repository-url&gt;
cd notification-engine/engine-system</code></pre>
                        </div>

                        <h3>2. Install Dependencies</h3>
                        <div class="code-block">
                            <pre><code># Using npm
npm install

# Using pnpm (recommended)
pnpm install</code></pre>
                        </div>

                        <h3>3. Environment Configuration</h3>
                        <div class="code-block">
                            <pre><code># Copy environment template
cp env.sample .env

# Edit environment variables
nano .env</code></pre>
                        </div>

                        <h4>Required Environment Variables</h4>
                        <div class="code-block">
                            <pre><code># Database
DATABASE_URL="mysql://user:password@localhost:3306/notification_engine"

# Redis
REDIS_URL="redis://localhost:6379"

# RabbitMQ
RABBITMQ_URL="amqp://localhost:5672"

# Email Configuration
SMTP_HOST="smtp.mailtrap.io"
SMTP_PORT=2525
SMTP_USER="your-username"
SMTP_PASS="your-password"

# Slack Configuration
SLACK_BOT_TOKEN="xoxb-your-bot-token"
SLACK_SIGNING_SECRET="your-signing-secret"

# HubSpot Configuration
HUBSPOT_API_KEY="your-api-key"

# Application
NODE_ENV="development"
PORT=3000
API_KEY="your-development-api-key"</code></pre>
                        </div>

                        <h3>4. Database Setup</h3>
                        <div class="code-block">
                            <pre><code># Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# Seed database with sample data
npx prisma db seed</code></pre>
                        </div>

                        <h3>5. Start Development Server</h3>
                        <div class="code-block">
                            <pre><code># Start with hot reload
npm run dev

# Or with pnpm
pnpm dev</code></pre>
                        </div>

                        <div class="highlight-box">
                            <h3>Docker Development Setup</h3>
                            <p>
                                For a quick setup with all dependencies, use
                                Docker Compose:
                            </p>
                            <div class="code-block">
                                <pre><code># Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down</code></pre>
                            </div>
                        </div>
                    </section>

                    <section id="project-structure" class="content-section">
                        <h2>Project Structure</h2>
                        <p>
                            Understanding the project structure is crucial for
                            effective development:
                        </p>

                        <div class="code-block">
                            <pre><code>src/
├── api/                    # REST API layer
│   ├── controllers/        # Request handlers
│   ├── routes/            # Route definitions
│   ├── middlewares.ts     # Custom middleware
│   ├── schemas/           # Validation schemas
│   └── utils/             # API utilities
├── jobs/                  # Background job definitions
│   ├── evaluation/        # Site evaluation jobs
│   ├── trigger/           # Trigger processing
│   ├── notification/      # Notification delivery
│   └── observation/       # Analytics jobs
├── messaging/             # Queue infrastructure
│   ├── queue-service.ts   # RabbitMQ service
│   ├── consumers/         # Message consumers
│   └── producers/         # Message producers
├── connectors/            # External integrations
│   ├── smtp.ts           # Email connector
│   ├── slack.ts          # Slack connector
│   └── hubspot.ts        # HubSpot connector
├── services/              # Business logic
├── types/                 # TypeScript definitions
├── utils/                 # Shared utilities
├── app.ts                 # Express app setup
├── server.ts              # Application entry
├── db.ts                  # Database connection
└── env.constant.ts        # Environment validation</code></pre>
                        </div>

                        <h3>Key Directories</h3>
                        <ul>
                            <li>
                                <strong>api/</strong> - All REST API related
                                code including controllers, routes, and
                                middleware
                            </li>
                            <li>
                                <strong>jobs/</strong> - Background job
                                definitions for queue processing
                            </li>
                            <li>
                                <strong>messaging/</strong> - Queue
                                infrastructure and message handling
                            </li>
                            <li>
                                <strong>connectors/</strong> - External service
                                integrations (email, Slack, HubSpot)
                            </li>
                            <li>
                                <strong>services/</strong> - Business logic and
                                service layer implementations
                            </li>
                            <li>
                                <strong>types/</strong> - TypeScript type
                                definitions and interfaces
                            </li>
                        </ul>
                    </section>

                    <section id="coding-standards" class="content-section">
                        <h2>Coding Standards</h2>

                        <h3>TypeScript Guidelines</h3>
                        <ul>
                            <li>Use strict TypeScript configuration</li>
                            <li>
                                Prefer interfaces over types for object shapes
                            </li>
                            <li>Use explicit return types for functions</li>
                            <li>
                                Avoid <code>any</code> type - use proper typing
                            </li>
                            <li>
                                Use enums for constants with multiple values
                            </li>
                        </ul>

                        <h3>Naming Conventions</h3>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h3>Files & Directories</h3>
                                <ul>
                                    <li>
                                        <strong>kebab-case</strong> for file
                                        names
                                    </li>
                                    <li>
                                        <strong>camelCase</strong> for directory
                                        names
                                    </li>
                                    <li>
                                        <strong>.service.ts</strong> for service
                                        files
                                    </li>
                                    <li>
                                        <strong>.controller.ts</strong> for
                                        controllers
                                    </li>
                                    <li>
                                        <strong>.types.ts</strong> for type
                                        definitions
                                    </li>
                                </ul>
                            </div>

                            <div class="feature-card">
                                <h3>Code Elements</h3>
                                <ul>
                                    <li>
                                        <strong>PascalCase</strong> for classes
                                        and interfaces
                                    </li>
                                    <li>
                                        <strong>camelCase</strong> for variables
                                        and functions
                                    </li>
                                    <li>
                                        <strong>UPPER_SNAKE_CASE</strong> for
                                        constants
                                    </li>
                                    <li>
                                        <strong>camelCase</strong> for object
                                        properties
                                    </li>
                                    <li>
                                        <strong>PascalCase</strong> for enum
                                        values
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <h3>Code Organization</h3>
                        <div class="code-block">
                            <pre><code>// Example service structure
export class NotificationService {
  constructor(
    private readonly notificationRepository: INotificationRepository,
    private readonly templateService: TemplateService,
    private readonly queueService: QueueService
  ) {}

  async sendNotification(request: SendNotificationRequest): Promise&lt;NotificationResponse&gt; {
    // Implementation
  }

  private async validateTemplate(templateId: string): Promise&lt;Template&gt; {
    // Private helper method
  }
}</code></pre>
                        </div>

                        <h3>Error Handling</h3>
                        <ul>
                            <li>
                                Use custom error classes for different error
                                types
                            </li>
                            <li>Always include meaningful error messages</li>
                            <li>Log errors with appropriate context</li>
                            <li>
                                Use HTTP status codes correctly in API responses
                            </li>
                        </ul>

                        <div class="code-block">
                            <pre><code>// Custom error example
export class TemplateNotFoundError extends Error {
  constructor(templateId: string) {
    super(`Template with ID ${templateId} not found`);
    this.name = 'TemplateNotFoundError';
  }
}</code></pre>
                        </div>
                    </section>

                    <section id="development-workflow" class="content-section">
                        <h2>Development Workflow</h2>

                        <h3>Git Workflow</h3>
                        <ol>
                            <li>
                                <strong>Create Feature Branch</strong> - Branch
                                from main for new features
                            </li>
                            <li>
                                <strong>Make Changes</strong> - Implement
                                feature with tests
                            </li>
                            <li>
                                <strong>Commit Changes</strong> - Use
                                conventional commit messages
                            </li>
                            <li>
                                <strong>Push Branch</strong> - Push to remote
                                repository
                            </li>
                            <li>
                                <strong>Create Pull Request</strong> - Submit
                                for code review
                            </li>
                            <li>
                                <strong>Code Review</strong> - Address feedback
                                and iterate
                            </li>
                            <li>
                                <strong>Merge</strong> - Merge after approval
                                and tests pass
                            </li>
                        </ol>

                        <h3>Commit Message Format</h3>
                        <div class="code-block">
                            <pre><code>type(scope): description

feat(api): add notification scheduling endpoint
fix(queue): resolve memory leak in job processor
docs(readme): update installation instructions
test(notification): add unit tests for email service</code></pre>
                        </div>

                        <h3>Available Scripts</h3>
                        <div class="code-block">
                            <pre><code># Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema changes
npm run db:seed      # Seed database
npm run db:reset     # Reset database

# Testing
npm run test         # Run all tests
npm run test:unit    # Run unit tests only
npm run test:integration # Run integration tests
npm run test:watch   # Run tests in watch mode

# Code Quality
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking</code></pre>
                        </div>
                    </section>

                    <section id="testing" class="content-section">
                        <h2>Testing</h2>

                        <h3>Testing Strategy</h3>
                        <ul>
                            <li>
                                <strong>Unit Tests</strong> - Test individual
                                functions and classes
                            </li>
                            <li>
                                <strong>Integration Tests</strong> - Test API
                                endpoints and database interactions
                            </li>
                            <li>
                                <strong>End-to-End Tests</strong> - Test
                                complete workflows
                            </li>
                        </ul>

                        <h3>Running Tests</h3>
                        <div class="code-block">
                            <pre><code># Run all tests
npm test

# Run specific test file
npm test notification.service.test.ts

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch</code></pre>
                        </div>

                        <h3>Writing Tests</h3>
                        <div class="code-block">
                            <pre><code>// Example unit test
describe('NotificationService', () => {
  let service: NotificationService;
  let mockRepository: jest.Mocked&lt;INotificationRepository&gt;;

  beforeEach(() => {
    mockRepository = createMockRepository();
    service = new NotificationService(mockRepository);
  });

  it('should send notification successfully', async () => {
    // Arrange
    const request = createNotificationRequest();
    mockRepository.save.mockResolvedValue(mockNotification);

    // Act
    const result = await service.sendNotification(request);

    // Assert
    expect(result.success).toBe(true);
    expect(mockRepository.save).toHaveBeenCalledWith(expect.any(Object));
  });
});</code></pre>
                        </div>
                    </section>

                    <section id="debugging" class="content-section">
                        <h2>Debugging</h2>

                        <h3>Logging</h3>
                        <p>
                            The application uses structured logging with Pino
                            for better debugging:
                        </p>

                        <div class="code-block">
                            <pre><code>import { logger } from '../utils/logger';

// Log levels: trace, debug, info, warn, error, fatal
logger.info('Processing notification', {
  notificationId: 'abc123',
  templateId: 'welcome-email',
  userId: '12345'
});

logger.error('Failed to send email', {
  error: error.message,
  stack: error.stack,
  notificationId: 'abc123'
});</code></pre>
                        </div>

                        <h3>VS Code Debugging</h3>
                        <p>
                            Use the following launch configuration for VS Code
                            debugging:
                        </p>

                        <div class="code-block">
                            <pre><code>// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/server.ts",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["-r", "tsx/cjs"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}</code></pre>
                        </div>

                        <h3>Common Debugging Scenarios</h3>
                        <ul>
                            <li>
                                <strong>Queue Issues</strong> - Check Bull Board
                                dashboard at <code>/admin/queues</code>
                            </li>
                            <li>
                                <strong>Database Issues</strong> - Use Prisma
                                Studio with <code>npx prisma studio</code>
                            </li>
                            <li>
                                <strong>Email Issues</strong> - Check SMTP logs
                                and Mailtrap inbox
                            </li>
                            <li>
                                <strong>API Issues</strong> - Use request
                                logging middleware and check response headers
                            </li>
                        </ul>
                    </section>
                </div>

                <!-- Page Navigation -->
                <nav class="page-nav" aria-label="Page navigation">
                    <a
                        href="../overview/project-overview.html"
                        class="page-nav-link page-nav-prev"
                    >
                        ← Project Overview
                    </a>
                    <a
                        href="../api/integration-guide.html"
                        class="page-nav-link page-nav-next"
                    >
                        Integration Guide →
                    </a>
                </nav>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer" role="contentinfo">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-title">Development</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="development-guide.html"
                                    >Development Guide</a
                                >
                            </li>
                            <li>
                                <a href="contributing.html">Contributing</a>
                            </li>
                            <li>
                                <a href="../operations/testing.html"
                                    >Testing Guide</a
                                >
                            </li>
                            <li>
                                <a
                                    href="../troubleshooting/troubleshooting-guide.html"
                                    >Troubleshooting</a
                                >
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Quick Links</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="../index.html">Documentation Home</a>
                            </li>
                            <li>
                                <a href="../overview/project-overview.html"
                                    >Project Overview</a
                                >
                            </li>
                            <li>
                                <a href="../api/api-reference.html"
                                    >API Reference</a
                                >
                            </li>
                            <li>
                                <a href="../operations/deployment.html"
                                    >Deployment</a
                                >
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p class="footer-copyright">
                        © 2025 Notification Engine Documentation. Built with ❤️
                        for Heatmap Inc.
                    </p>
                </div>
            </div>
        </footer>
    </body>
</html>
