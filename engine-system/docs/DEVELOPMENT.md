# Development Workflow Guide

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Coding Standards](#coding-standards)
- [Project Structure Guidelines](#project-structure-guidelines)
- [Development Workflow](#development-workflow)
- [Testing Procedures](#testing-procedures)
- [Code Review Process](#code-review-process)
- [Debugging Guidelines](#debugging-guidelines)
- [Performance Considerations](#performance-considerations)

## Development Environment Setup

### Prerequisites

Ensure you have the following installed:

```bash
# Required versions
Node.js >= 18.0.0
npm >= 8.0.0 (or pnpm >= 7.0.0)
MySQL >= 8.0
Redis >= 6.0
RabbitMQ >= 3.8
```

### Initial Setup

1. **Clone and Install Dependencies**
   ```bash
   git clone <repository-url>
   cd notification-engine/engine-system
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp env.sample .env
   ```
   
   Configure the following essential variables:
   ```bash
   # Development settings
   NODE_ENV=development
   PORT=3000
   BUILD_TARGET=dev
   
   # Database
   DATABASE_URL=mysql://user:password@localhost:3306/notifications_dev
   
   # Redis
   REDIS_HOST=localhost
   REDIS_PORT=6379
   
   # RabbitMQ
   RABBITMQ_HOST=localhost
   RABBITMQ_USERNAME=guest
   RABBITMQ_PASSWORD=guest
   
   # Email (for testing)
   EMAIL_ON=false  # Set to true when testing email functionality
   SMTP_HOST=smtp.mailtrap.io  # Use Mailtrap for development
   ```

3. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push schema to database
   npx prisma db push
   
   # Seed development data
   npm run seed
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

### Development Tools Setup

1. **VS Code Extensions** (Recommended)
   - TypeScript and JavaScript Language Features
   - Prisma
   - ESLint
   - Prettier
   - Thunder Client (for API testing)

2. **Database Management**
   ```bash
   # Open Prisma Studio for database GUI
   npx prisma studio
   ```

3. **Queue Monitoring**
   - Access Bull Board at: `http://localhost:3000/admin/queues`
   - Monitor job processing and queue health

## Coding Standards

### TypeScript Guidelines

1. **Type Safety**
   ```typescript
   // ✅ Good: Explicit types
   interface UserData {
     id: number;
     email: string;
     preferences: NotificationPreference[];
   }
   
   // ❌ Avoid: Any types
   function processUser(data: any) { }
   
   // ✅ Good: Proper typing
   function processUser(data: UserData): Promise<ProcessResult> { }
   ```

2. **Interface Definitions**
   ```typescript
   // ✅ Good: Clear interface naming
   interface INotificationService {
     sendNotification(data: NotificationData): Promise<void>;
   }
   
   // ✅ Good: Extend base interfaces
   interface EmailNotificationData extends NotificationData {
     subject: string;
     htmlBody: string;
   }
   ```

3. **Error Handling**
   ```typescript
   // ✅ Good: Specific error types
   class NotificationError extends Error {
     constructor(
       message: string,
       public readonly code: string,
       public readonly channel: string
     ) {
       super(message);
       this.name = 'NotificationError';
     }
   }
   
   // ✅ Good: Proper error handling
   try {
     await sendNotification(data);
   } catch (error) {
     if (error instanceof NotificationError) {
       logger.error('Notification failed', { 
         code: error.code, 
         channel: error.channel 
       });
     }
     throw error;
   }
   ```

### Code Organization

1. **File Naming Conventions**
   ```
   ✅ Good:
   - user-service.ts
   - notification-controller.ts
   - email-connector.ts
   - trigger-queue.ts
   
   ❌ Avoid:
   - UserService.ts
   - notificationController.ts
   - emailconnector.ts
   ```

2. **Class and Function Naming**
   ```typescript
   // ✅ Good: Clear, descriptive names
   class NotificationService {
     async sendEmailNotification(data: EmailData): Promise<void> { }
     async validateTemplateData(template: string, data: object): Promise<boolean> { }
   }
   
   // ✅ Good: Consistent naming patterns
   interface IEmailConnector {
     connect(): Promise<void>;
     sendEmail(data: EmailData): Promise<SendResult>;
     disconnect(): Promise<void>;
   }
   ```

3. **Import Organization**
   ```typescript
   // ✅ Good: Organized imports
   // Node.js built-ins
   import * as fs from 'fs';
   import * as path from 'path';
   
   // Third-party packages
   import express from 'express';
   import { Job } from 'bullmq';
   
   // Internal modules
   import { NotificationService } from '../services/notification-service';
   import { EmailConnector } from '../connectors/email-connector';
   import { Logger } from '../utils/logger';
   
   // Types
   import type { NotificationData, SendResult } from '../types';
   ```

### Documentation Standards

1. **Function Documentation**
   ```typescript
   /**
    * Sends a notification through the specified channel
    * @param data - The notification data including recipient and content
    * @param channel - The delivery channel (email, slack, hubspot)
    * @param options - Optional delivery options
    * @returns Promise resolving to delivery result
    * @throws NotificationError when delivery fails
    */
   async function sendNotification(
     data: NotificationData,
     channel: ChannelType,
     options?: DeliveryOptions
   ): Promise<DeliveryResult> {
     // Implementation
   }
   ```

2. **Class Documentation**
   ```typescript
   /**
    * Service for managing notification templates and rendering
    * 
    * Handles template loading, validation, and rendering for multiple channels.
    * Supports Handlebars templating with custom helpers and layouts.
    * 
    * @example
    * ```typescript
    * const service = new TemplateService();
    * const rendered = await service.renderTemplate('welcome-email', userData);
    * ```
    */
   class TemplateService {
     // Implementation
   }
   ```

## Project Structure Guidelines

### Module Organization

```
src/
├── api/                    # API layer
│   ├── controllers/        # Request handlers (max 20 lines per method)
│   ├── routes/            # Route definitions
│   ├── middleware/        # Custom middleware
│   └── schemas/           # Validation schemas
├── services/              # Business logic (single responsibility)
├── connectors/            # External service integrations
├── jobs/                  # Queue job definitions
├── messaging/             # Queue and messaging
├── types/                 # TypeScript definitions
└── utils/                 # Shared utilities
```

### File Organization Rules

1. **Single Responsibility**: One class/service per file
2. **Max File Size**: Keep files under 300 lines
3. **Clear Dependencies**: Minimize circular dependencies
4. **Consistent Exports**: Use named exports for clarity

### Dependency Injection Pattern

```typescript
// ✅ Good: Constructor injection
class NotificationController {
  constructor(
    private readonly notificationService: INotificationService,
    private readonly templateService: ITemplateService,
    private readonly logger: ILogger
  ) {}
}

// ✅ Good: Interface-based dependencies
interface INotificationService {
  sendNotification(data: NotificationData): Promise<void>;
}
```

## Development Workflow

### Branch Strategy

1. **Main Branches**
   - `main`: Production-ready code
   - `develop`: Integration branch for features
   - `staging`: Pre-production testing

2. **Feature Branches**
   ```bash
   # Create feature branch
   git checkout -b feature/notification-retry-logic
   
   # Work on feature
   git add .
   git commit -m "feat: add exponential backoff for notification retries"
   
   # Push and create PR
   git push origin feature/notification-retry-logic
   ```

3. **Commit Message Format**
   ```bash
   # Format: type(scope): description
   feat(notifications): add retry mechanism for failed deliveries
   fix(templates): resolve handlebars helper registration issue
   docs(api): update endpoint documentation
   test(services): add unit tests for notification service
   refactor(queues): improve job processing performance
   ```

### Development Process

1. **Before Starting Work**
   ```bash
   # Update local repository
   git checkout develop
   git pull origin develop
   
   # Create feature branch
   git checkout -b feature/your-feature-name
   ```

2. **During Development**
   ```bash
   # Run tests frequently
   npm test
   
   # Check code formatting
   npm run format:check
   
   # Validate TypeScript
   npm run type-check
   ```

3. **Before Committing**
   ```bash
   # Format code
   npm run format
   
   # Run full test suite
   npm test
   
   # Check for linting issues
   npm run lint
   ```

### Environment Management

1. **Development Environment**
   ```bash
   NODE_ENV=development
   EMAIL_ON=false  # Use logging instead of actual emails
   LOGGER_TOGGLE=true
   ```

2. **Testing Environment**
   ```bash
   NODE_ENV=test
   DATABASE_URL=mysql://user:password@localhost:3306/notifications_test
   ```

3. **Staging Environment**
   ```bash
   NODE_ENV=staging
   EMAIL_ON=true
   SMTP_HOST=smtp.mailtrap.io  # Use Mailtrap for staging
   ```

## Testing Procedures

### Test Structure

```
tests/
├── unit/                  # Unit tests for individual components
│   ├── services/         # Service layer tests
│   ├── connectors/       # Connector tests
│   └── utils/            # Utility function tests
├── integration/          # Integration tests
│   ├── api/              # API endpoint tests
│   ├── database/         # Database operation tests
│   └── queues/           # Queue processing tests
└── helpers/              # Test utilities and fixtures
```

### Testing Guidelines

1. **Unit Tests**
   ```typescript
   // ✅ Good: Test single responsibility
   describe('NotificationService', () => {
     describe('sendNotification', () => {
       it('should send email notification successfully', async () => {
         // Arrange
         const mockEmailConnector = createMockEmailConnector();
         const service = new NotificationService(mockEmailConnector);
         const notificationData = createTestNotificationData();
         
         // Act
         const result = await service.sendNotification(notificationData);
         
         // Assert
         expect(result.success).toBe(true);
         expect(mockEmailConnector.sendEmail).toHaveBeenCalledWith(
           expect.objectContaining({
             to: notificationData.recipient,
             subject: expect.any(String)
           })
         );
       });
     });
   });
   ```

2. **Integration Tests**
   ```typescript
   // ✅ Good: Test complete workflows
   describe('Notification API', () => {
     it('should process real-time notification end-to-end', async () => {
       // Setup test database
       await setupTestDatabase();
       
       // Send API request
       const response = await request(app)
         .post('/real-time')
         .send(testNotificationPayload)
         .expect(200);
       
       // Verify database state
       const notification = await findNotificationInDatabase(response.body.id);
       expect(notification.status).toBe('sent');
       
       // Cleanup
       await cleanupTestDatabase();
     });
   });
   ```

### Test Commands

```bash
# Run all tests
npm test

# Run specific test suite
npm test -- --grep "NotificationService"

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Code Review Process

### Review Checklist

1. **Code Quality**
   - [ ] Follows TypeScript best practices
   - [ ] Proper error handling
   - [ ] No hardcoded values
   - [ ] Consistent naming conventions

2. **Architecture**
   - [ ] Follows SOLID principles
   - [ ] Proper separation of concerns
   - [ ] Appropriate use of design patterns
   - [ ] No circular dependencies

3. **Testing**
   - [ ] Adequate test coverage
   - [ ] Tests are meaningful and not brittle
   - [ ] Integration tests for new features
   - [ ] Edge cases are covered

4. **Documentation**
   - [ ] Code is self-documenting
   - [ ] Complex logic is commented
   - [ ] API changes are documented
   - [ ] README updates if needed

### Review Guidelines

1. **For Reviewers**
   - Focus on logic, architecture, and maintainability
   - Suggest improvements, don't just point out problems
   - Test the changes locally when possible
   - Approve only when confident in the changes

2. **For Authors**
   - Provide context in PR description
   - Respond to feedback constructively
   - Make requested changes promptly
   - Ensure CI passes before requesting review

## Debugging Guidelines

### Logging Best Practices

```typescript
// ✅ Good: Structured logging
logger.info('Processing notification', {
  notificationId: notification.id,
  channel: notification.channel,
  recipient: notification.recipient,
  templateName: notification.templateName
});

// ✅ Good: Error logging with context
logger.error('Failed to send notification', {
  error: error.message,
  stack: error.stack,
  notificationId: notification.id,
  channel: notification.channel
});
```

### Debugging Tools

1. **Queue Monitoring**
   - Access Bull Board: `http://localhost:3000/admin/queues`
   - Monitor job failures and retry attempts

2. **Database Debugging**
   ```bash
   # Open Prisma Studio
   npx prisma studio
   
   # View recent notifications
   # Check notification_logs table for delivery status
   ```

3. **API Debugging**
   ```bash
   # Test endpoints with curl
   curl -X POST http://localhost:3000/real-time \
     -H "Content-Type: application/json" \
     -d '{"idSite": 123, "triggerName": "test"}'
   ```

### Common Issues and Solutions

1. **Queue Jobs Not Processing**
   - Check Redis connection
   - Verify queue worker is running
   - Check job data validation

2. **Template Rendering Errors**
   - Validate template syntax
   - Check template data schema
   - Verify helper functions are registered

3. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify MySQL server is running
   - Check connection pool settings

## Performance Considerations

### Code Performance

1. **Async/Await Best Practices**
   ```typescript
   // ✅ Good: Parallel processing
   const [user, preferences, templates] = await Promise.all([
     getUserById(userId),
     getNotificationPreferences(userId),
     getTemplates(templateIds)
   ]);
   
   // ❌ Avoid: Sequential processing
   const user = await getUserById(userId);
   const preferences = await getNotificationPreferences(userId);
   const templates = await getTemplates(templateIds);
   ```

2. **Database Query Optimization**
   ```typescript
   // ✅ Good: Include related data
   const notifications = await prisma.notificationLog.findMany({
     include: {
       trigger: true,
       template: true
     },
     where: { siteId: siteId }
   });
   
   // ❌ Avoid: N+1 queries
   const notifications = await prisma.notificationLog.findMany();
   for (const notification of notifications) {
     const trigger = await prisma.trigger.findUnique({
       where: { id: notification.triggerId }
     });
   }
   ```

### Memory Management

1. **Large Data Processing**
   ```typescript
   // ✅ Good: Stream processing for large datasets
   async function processLargeDataset(data: AsyncIterable<Item>) {
     for await (const item of data) {
       await processItem(item);
     }
   }
   
   // ❌ Avoid: Loading everything into memory
   const allItems = await loadAllItems();
   for (const item of allItems) {
     await processItem(item);
   }
   ```

2. **Queue Job Memory**
   - Keep job data minimal
   - Use database references instead of full objects
   - Clean up temporary data after processing

---

**Next Steps**: Review the [Testing Guide](TESTING.md) for comprehensive testing strategies and the [API Documentation](API.md) for endpoint specifications.
