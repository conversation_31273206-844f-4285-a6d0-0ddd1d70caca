# Integration Guide

## Table of Contents

- [Overview](#overview)
- [Quick Start Integration](#quick-start-integration)
- [Authentication Setup](#authentication-setup)
- [Channel Integrations](#channel-integrations)
- [Webhook Integration](#webhook-integration)
- [SDK and Client Libraries](#sdk-and-client-libraries)
- [Common Integration Patterns](#common-integration-patterns)
- [Error Handling](#error-handling)
- [Testing Your Integration](#testing-your-integration)

## Overview

The Notification Engine provides multiple integration methods to fit different use cases and technical requirements. This guide covers the most common integration scenarios and provides practical examples for each.

### Integration Methods

1. **REST API**: Direct HTTP calls for real-time notifications
2. **Webhook Integration**: Event-driven notifications from your application
3. **SDK Libraries**: Pre-built clients for popular languages
4. **Queue Integration**: Direct queue publishing for high-volume scenarios
5. **Database Triggers**: Database-level integration for automatic notifications

## Quick Start Integration

### 1. Basic Real-Time Notification

The simplest integration sends immediate notifications via the REST API:

```javascript
// Send a welcome email when user signs up
async function sendWelcomeNotification(userId, userData) {
  const response = await fetch('http://localhost:3000/real-time', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      idSite: userData.siteId,
      triggerName: 'user-welcome',
      templateData: {
        userName: userData.name,
        email: userData.email,
        activationUrl: `https://yourapp.com/activate/${userData.activationToken}`
      },
      channels: ['email']
    })
  });

  if (!response.ok) {
    throw new Error(`Notification failed: ${response.status}`);
  }

  return response.json();
}
```

### 2. Trigger-Based Evaluation

For automated notifications based on conditions:

```javascript
// Trigger weekly summary for all active sites
async function triggerWeeklySummaries() {
  const activeSites = await getActiveSites();
  
  const response = await fetch('http://localhost:3000/trigger/evaluation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      sites: activeSites.map(site => site.id),
      triggerTypes: ['weekly-summary'],
      immediate: false // Use scheduling
    })
  });

  return response.json();
}
```

## Authentication Setup

### API Key Authentication

1. **Generate API Key** (when implemented)
   ```bash
   # Generate a new API key
   curl -X POST http://localhost:3000/auth/api-keys \
     -H "Authorization: Bearer ADMIN_TOKEN" \
     -d '{"name": "My Integration", "permissions": ["notifications:send"]}'
   ```

2. **Use API Key in Requests**
   ```javascript
   const headers = {
     'Authorization': 'Bearer YOUR_API_KEY',
     'Content-Type': 'application/json'
   };
   ```

### Environment-Based Configuration

```javascript
// Configuration for different environments
const config = {
  development: {
    baseUrl: 'http://localhost:3000',
    apiKey: process.env.DEV_API_KEY
  },
  staging: {
    baseUrl: 'https://staging-notifications.yourapp.com',
    apiKey: process.env.STAGING_API_KEY
  },
  production: {
    baseUrl: 'https://notifications.yourapp.com',
    apiKey: process.env.PROD_API_KEY
  }
};

const notificationConfig = config[process.env.NODE_ENV];
```

## Channel Integrations

### Email Integration

#### SMTP Configuration
```bash
# Environment variables for email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
DEFAULT_EMAIL_FROM=<EMAIL>
```

#### Email Notification Example
```javascript
async function sendEmailNotification(userData) {
  return await fetch('http://localhost:3000/real-time', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      idSite: userData.siteId,
      triggerName: 'password-reset',
      templateData: {
        userName: userData.name,
        resetUrl: userData.resetUrl,
        expiresIn: '24 hours'
      },
      channels: ['email']
    })
  });
}
```

### Slack Integration

#### OAuth Setup
1. **Create Slack App**: Visit https://api.slack.com/apps
2. **Configure OAuth Scopes**:
   - `chat:write`
   - `channels:read`
   - `groups:read`

3. **Set Redirect URL**: `https://yourapp.com/slack/callback`

#### Slack Authentication Flow
```javascript
// Step 1: Redirect user to Slack OAuth
function initiateSlackAuth(siteId) {
  const params = new URLSearchParams({
    client_id: process.env.SLACK_CLIENT_ID,
    scope: 'chat:write,channels:read',
    redirect_uri: `${process.env.BASE_URL}/slack/callback`,
    state: siteId
  });
  
  return `https://slack.com/oauth/v2/authorize?${params}`;
}

// Step 2: Handle OAuth callback
async function handleSlackCallback(code, state) {
  const response = await fetch('http://localhost:3000/slack/auth', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      code,
      state,
      idSite: parseInt(state)
    })
  });
  
  return response.json();
}
```

#### Send Slack Notification
```javascript
async function sendSlackAlert(alertData) {
  return await fetch('http://localhost:3000/real-time', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      idSite: alertData.siteId,
      triggerName: 'system-alert',
      templateData: {
        alertType: alertData.type,
        message: alertData.message,
        severity: alertData.severity,
        timestamp: new Date().toISOString()
      },
      channels: ['slack']
    })
  });
}
```

### HubSpot CRM Integration

#### Contact Sync Example
```javascript
async function syncContactToHubSpot(contactData) {
  return await fetch('http://localhost:3000/real-time', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      idSite: contactData.siteId,
      triggerName: 'contact-sync',
      templateData: {
        email: contactData.email,
        firstName: contactData.firstName,
        lastName: contactData.lastName,
        company: contactData.company,
        properties: contactData.customProperties
      },
      channels: ['hubspot']
    })
  });
}
```

## Webhook Integration

### Setting Up Webhooks

1. **Configure Webhook Endpoint**
   ```javascript
   // Your webhook endpoint
   app.post('/webhooks/notifications', (req, res) => {
     const event = req.body;
     
     switch (event.type) {
       case 'user.created':
         handleUserCreated(event.data);
         break;
       case 'subscription.expired':
         handleSubscriptionExpired(event.data);
         break;
       case 'trial.ending':
         handleTrialEnding(event.data);
         break;
     }
     
     res.status(200).json({ received: true });
   });
   ```

2. **Webhook Event Handlers**
   ```javascript
   async function handleUserCreated(userData) {
     // Send welcome email
     await sendNotification({
       idSite: userData.siteId,
       triggerName: 'user-welcome',
       templateData: userData,
       channels: ['email']
     });
   }

   async function handleTrialEnding(trialData) {
     // Send trial ending reminder
     await sendNotification({
       idSite: trialData.siteId,
       triggerName: 'trial-ending',
       templateData: {
         userName: trialData.userName,
         daysLeft: trialData.daysLeft,
         upgradeUrl: trialData.upgradeUrl
       },
       channels: ['email', 'slack']
     });
   }
   ```

### Webhook Security

```javascript
// Verify webhook signatures
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
    
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

app.post('/webhooks/notifications', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const isValid = verifyWebhookSignature(
    JSON.stringify(req.body),
    signature,
    process.env.WEBHOOK_SECRET
  );
  
  if (!isValid) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  // Process webhook...
});
```

## SDK and Client Libraries

### JavaScript/Node.js SDK

```javascript
class NotificationClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async send(notification) {
    const response = await fetch(`${this.baseUrl}/real-time`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify(notification)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new NotificationError(error.error.message, error.error.code);
    }

    return response.json();
  }

  async triggerEvaluation(sites, options = {}) {
    const response = await fetch(`${this.baseUrl}/trigger/evaluation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({ sites, ...options })
    });

    return response.json();
  }

  // Convenience methods
  async sendWelcomeEmail(siteId, userData) {
    return this.send({
      idSite: siteId,
      triggerName: 'user-welcome',
      templateData: userData,
      channels: ['email']
    });
  }

  async sendSlackAlert(siteId, alertData) {
    return this.send({
      idSite: siteId,
      triggerName: 'system-alert',
      templateData: alertData,
      channels: ['slack']
    });
  }
}

// Usage
const client = new NotificationClient(
  process.env.NOTIFICATION_API_URL,
  process.env.NOTIFICATION_API_KEY
);

await client.sendWelcomeEmail(123, {
  userName: 'John Doe',
  email: '<EMAIL>'
});
```

### Python SDK

```python
import requests
from typing import Dict, List, Optional

class NotificationClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

    def send(self, notification: Dict) -> Dict:
        response = requests.post(
            f'{self.base_url}/real-time',
            headers=self.headers,
            json=notification
        )
        response.raise_for_status()
        return response.json()

    def trigger_evaluation(self, sites: List[int], **options) -> Dict:
        data = {'sites': sites, **options}
        response = requests.post(
            f'{self.base_url}/trigger/evaluation',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()

    def send_welcome_email(self, site_id: int, user_data: Dict) -> Dict:
        return self.send({
            'idSite': site_id,
            'triggerName': 'user-welcome',
            'templateData': user_data,
            'channels': ['email']
        })

# Usage
client = NotificationClient(
    os.getenv('NOTIFICATION_API_URL'),
    os.getenv('NOTIFICATION_API_KEY')
)

client.send_welcome_email(123, {
    'userName': 'John Doe',
    'email': '<EMAIL>'
})
```

## Common Integration Patterns

### 1. User Lifecycle Notifications

```javascript
class UserLifecycleNotifications {
  constructor(notificationClient) {
    this.client = notificationClient;
  }

  async onUserRegistered(user) {
    await this.client.send({
      idSite: user.siteId,
      triggerName: 'user-welcome',
      templateData: {
        userName: user.name,
        email: user.email,
        activationUrl: user.activationUrl
      },
      channels: ['email']
    });
  }

  async onUserActivated(user) {
    await this.client.send({
      idSite: user.siteId,
      triggerName: 'user-activated',
      templateData: {
        userName: user.name,
        dashboardUrl: user.dashboardUrl
      },
      channels: ['email', 'slack']
    });
  }

  async onUserInactive(user, daysSinceLogin) {
    await this.client.send({
      idSite: user.siteId,
      triggerName: 'user-inactive',
      templateData: {
        userName: user.name,
        daysSinceLogin,
        loginUrl: user.loginUrl
      },
      channels: ['email']
    });
  }
}
```

### 2. Business Metrics Notifications

```javascript
class MetricsNotifications {
  constructor(notificationClient) {
    this.client = notificationClient;
  }

  async sendWeeklyReport(siteId, metrics) {
    await this.client.send({
      idSite: siteId,
      triggerName: 'weekly-summary',
      templateData: {
        siteName: metrics.siteName,
        startDate: metrics.startDate,
        endDate: metrics.endDate,
        metrics: {
          sessions: metrics.sessions,
          revenue: metrics.revenue,
          pageviews: metrics.pageviews,
          conversionRate: metrics.conversionRate
        },
        topPages: metrics.topPages
      },
      channels: ['email', 'slack']
    });
  }

  async sendRevenueAlert(siteId, revenueData) {
    await this.client.send({
      idSite: siteId,
      triggerName: 'revenue-milestone',
      templateData: {
        milestone: revenueData.milestone,
        currentRevenue: revenueData.current,
        previousPeriod: revenueData.previous,
        growthRate: revenueData.growthRate
      },
      channels: ['slack']
    });
  }
}
```

### 3. System Monitoring Integration

```javascript
class SystemMonitoring {
  constructor(notificationClient) {
    this.client = notificationClient;
  }

  async sendErrorAlert(error, context) {
    await this.client.send({
      idSite: context.siteId,
      triggerName: 'system-error',
      templateData: {
        errorMessage: error.message,
        errorStack: error.stack,
        timestamp: new Date().toISOString(),
        severity: this.determineSeverity(error),
        context: context
      },
      channels: ['slack'],
      priority: 'high'
    });
  }

  async sendPerformanceAlert(metrics) {
    await this.client.send({
      idSite: metrics.siteId,
      triggerName: 'performance-alert',
      templateData: {
        metric: metrics.metric,
        currentValue: metrics.current,
        threshold: metrics.threshold,
        trend: metrics.trend
      },
      channels: ['slack']
    });
  }

  determineSeverity(error) {
    if (error.name === 'DatabaseError') return 'critical';
    if (error.name === 'ValidationError') return 'low';
    return 'medium';
  }
}
```

## Error Handling

### Retry Logic

```javascript
class NotificationClientWithRetry {
  constructor(baseClient, maxRetries = 3) {
    this.client = baseClient;
    this.maxRetries = maxRetries;
  }

  async sendWithRetry(notification, retryCount = 0) {
    try {
      return await this.client.send(notification);
    } catch (error) {
      if (retryCount < this.maxRetries && this.isRetryableError(error)) {
        const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
        await this.sleep(delay);
        return this.sendWithRetry(notification, retryCount + 1);
      }
      throw error;
    }
  }

  isRetryableError(error) {
    return error.code === 'QUEUE_UNAVAILABLE' || 
           error.code === 'INTERNAL_ERROR' ||
           error.status >= 500;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Error Monitoring

```javascript
class NotificationErrorHandler {
  constructor(logger, alerting) {
    this.logger = logger;
    this.alerting = alerting;
  }

  async handleError(error, context) {
    // Log the error
    this.logger.error('Notification failed', {
      error: error.message,
      code: error.code,
      context: context,
      timestamp: new Date().toISOString()
    });

    // Send alert for critical errors
    if (this.isCriticalError(error)) {
      await this.alerting.sendAlert({
        type: 'notification_failure',
        severity: 'high',
        message: `Notification system error: ${error.message}`,
        context: context
      });
    }

    // Store for analysis
    await this.storeErrorMetrics(error, context);
  }

  isCriticalError(error) {
    return error.code === 'QUEUE_UNAVAILABLE' ||
           error.code === 'DATABASE_ERROR' ||
           error.status >= 500;
  }
}
```

## Testing Your Integration

### Unit Tests

```javascript
describe('Notification Integration', () => {
  let mockClient;

  beforeEach(() => {
    mockClient = {
      send: jest.fn().mockResolvedValue({ success: true })
    };
  });

  test('should send welcome notification', async () => {
    const lifecycle = new UserLifecycleNotifications(mockClient);
    
    await lifecycle.onUserRegistered({
      siteId: 123,
      name: 'John Doe',
      email: '<EMAIL>',
      activationUrl: 'https://app.com/activate/token'
    });

    expect(mockClient.send).toHaveBeenCalledWith({
      idSite: 123,
      triggerName: 'user-welcome',
      templateData: expect.objectContaining({
        userName: 'John Doe',
        email: '<EMAIL>'
      }),
      channels: ['email']
    });
  });
});
```

### Integration Tests

```javascript
describe('End-to-End Notification Flow', () => {
  test('should send and deliver email notification', async () => {
    const client = new NotificationClient(
      process.env.TEST_API_URL,
      process.env.TEST_API_KEY
    );

    const result = await client.send({
      idSite: 123,
      triggerName: 'test-notification',
      templateData: {
        userName: 'Test User',
        message: 'This is a test'
      },
      channels: ['email']
    });

    expect(result.success).toBe(true);
    expect(result.notificationId).toBeDefined();

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify delivery (if test email service is configured)
    const deliveryStatus = await checkDeliveryStatus(result.notificationId);
    expect(deliveryStatus).toBe('delivered');
  });
});
```

---

**Next Steps**: 
- Review the [API Documentation](API.md) for detailed endpoint specifications
- Check the [Template System](TEMPLATES.md) guide for creating custom templates
- See the [Troubleshooting Guide](TROUBLESHOOTING.md) for common integration issues
