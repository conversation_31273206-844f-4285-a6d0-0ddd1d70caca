# Contributing Guide

Thank you for your interest in contributing to the Notification Engine! This guide will help you understand our development process and how to contribute effectively.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Standards](#code-standards)
- [Testing Requirements](#testing-requirements)
- [Pull Request Process](#pull-request-process)
- [Documentation Guidelines](#documentation-guidelines)
- [Issue Reporting](#issue-reporting)
- [Community Guidelines](#community-guidelines)

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- Node.js 18+ installed
- MySQL 8.0+ running locally
- Redis 6.0+ running locally
- RabbitMQ 3.8+ running locally
- Git configured with your GitHub account

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/notification-engine.git
   cd notification-engine/engine-system
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp env.sample .env
   # Configure your local environment variables
   ```

4. **Database Setup**
   ```bash
   npx prisma generate
   npx prisma db push
   npm run seed
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

6. **Verify Setup**
   - Visit `http://localhost:3000/up` for health check
   - Visit `http://localhost:3000/admin/queues` for queue dashboard
   - Visit `http://localhost:3000/docs` for documentation

## Development Workflow

### Branch Strategy

We use a Git flow-based branching strategy:

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Feature development branches
- **hotfix/**: Critical bug fixes
- **release/**: Release preparation branches

### Creating a Feature Branch

```bash
# Start from develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name

# Work on your feature
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(notifications): add retry mechanism for failed deliveries
fix(templates): resolve handlebars helper registration issue
docs(api): update endpoint documentation
test(services): add unit tests for notification service
```

## Code Standards

### TypeScript Guidelines

1. **Type Safety**
   ```typescript
   // ✅ Good: Explicit types
   interface NotificationData {
     id: string;
     recipient: string;
     content: string;
   }
   
   // ❌ Avoid: Any types
   function processData(data: any) { }
   
   // ✅ Good: Proper typing
   function processNotification(data: NotificationData): Promise<void> { }
   ```

2. **Error Handling**
   ```typescript
   // ✅ Good: Specific error types
   class NotificationError extends Error {
     constructor(
       message: string,
       public readonly code: string
     ) {
       super(message);
       this.name = 'NotificationError';
     }
   }
   ```

3. **Async/Await**
   ```typescript
   // ✅ Good: Proper error handling
   try {
     const result = await sendNotification(data);
     return result;
   } catch (error) {
     logger.error('Notification failed', { error: error.message });
     throw error;
   }
   ```

### Code Organization

1. **File Structure**
   - One class/service per file
   - Clear, descriptive file names
   - Consistent import organization

2. **Function Guidelines**
   - Maximum 20 lines per function
   - Single responsibility principle
   - Clear, descriptive names

3. **Class Guidelines**
   - Constructor dependency injection
   - Interface-based dependencies
   - Clear separation of concerns

### Linting and Formatting

```bash
# Check code formatting
npm run lint

# Fix formatting issues
npm run lint:fix

# Format code with Prettier
npm run format
```

## Testing Requirements

### Test Coverage

All contributions must include appropriate tests:

- **Unit Tests**: For individual functions and classes
- **Integration Tests**: For API endpoints and workflows
- **Minimum Coverage**: 80% for new code

### Writing Tests

1. **Unit Tests**
   ```typescript
   describe('NotificationService', () => {
     describe('sendNotification', () => {
       it('should send email notification successfully', async () => {
         // Arrange
         const mockConnector = createMockEmailConnector();
         const service = new NotificationService(mockConnector);
         
         // Act
         const result = await service.sendNotification(testData);
         
         // Assert
         expect(result.success).toBe(true);
       });
     });
   });
   ```

2. **Integration Tests**
   ```typescript
   describe('POST /real-time', () => {
     it('should process notification request', async () => {
       const response = await request(app)
         .post('/real-time')
         .send(validNotificationData)
         .expect(200);
         
       expect(response.body.success).toBe(true);
     });
   });
   ```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test -- notification.service.test.ts

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Pull Request Process

### Before Submitting

1. **Code Quality**
   - [ ] All tests pass
   - [ ] Code follows style guidelines
   - [ ] No linting errors
   - [ ] Adequate test coverage

2. **Documentation**
   - [ ] Code is self-documenting
   - [ ] Complex logic is commented
   - [ ] API changes are documented
   - [ ] README updated if needed

### PR Template

When creating a pull request, include:

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

### Review Process

1. **Automated Checks**
   - CI/CD pipeline must pass
   - All tests must pass
   - Code coverage requirements met

2. **Code Review**
   - At least one team member approval required
   - Address all review comments
   - Maintain clean commit history

3. **Merge Requirements**
   - All checks passing
   - Approved by reviewer(s)
   - Up-to-date with target branch

## Documentation Guidelines

### Code Documentation

1. **Function Documentation**
   ```typescript
   /**
    * Sends a notification through the specified channel
    * @param data - The notification data
    * @param channel - The delivery channel
    * @returns Promise resolving to delivery result
    * @throws NotificationError when delivery fails
    */
   async function sendNotification(
     data: NotificationData,
     channel: ChannelType
   ): Promise<DeliveryResult> {
     // Implementation
   }
   ```

2. **Class Documentation**
   ```typescript
   /**
    * Service for managing notification delivery
    * 
    * Handles notification processing, template rendering,
    * and delivery through multiple channels.
    */
   class NotificationService {
     // Implementation
   }
   ```

### Markdown Documentation

1. **Structure**
   - Clear headings and sections
   - Table of contents for long documents
   - Code examples with syntax highlighting

2. **Style**
   - Use present tense
   - Be concise but comprehensive
   - Include practical examples

## Issue Reporting

### Bug Reports

When reporting bugs, include:

1. **Environment Information**
   - Node.js version
   - Operating system
   - Database versions

2. **Steps to Reproduce**
   - Clear, numbered steps
   - Expected vs actual behavior
   - Error messages and logs

3. **Additional Context**
   - Screenshots if applicable
   - Related issues or PRs
   - Possible solutions

### Feature Requests

When requesting features, include:

1. **Problem Description**
   - What problem does this solve?
   - Who would benefit from this feature?

2. **Proposed Solution**
   - Detailed description of the feature
   - Alternative solutions considered

3. **Implementation Notes**
   - Technical considerations
   - Breaking changes
   - Migration requirements

## Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment:

- **Be Respectful**: Treat all contributors with respect
- **Be Collaborative**: Work together towards common goals
- **Be Inclusive**: Welcome newcomers and diverse perspectives
- **Be Professional**: Maintain professional communication

### Communication

- **Issues**: Use GitHub issues for bug reports and feature requests
- **Discussions**: Use GitHub discussions for questions and ideas
- **Pull Requests**: Use PR comments for code-specific discussions
- **Team Chat**: Use internal channels for team coordination

### Recognition

We value all contributions:

- **Contributors**: Listed in project documentation
- **Maintainers**: Recognized for ongoing contributions
- **Community**: Acknowledged in release notes

---

## Getting Help

If you need help with contributing:

1. **Documentation**: Check the [Development Guide](DEVELOPMENT.md)
2. **Issues**: Search existing issues for similar problems
3. **Discussions**: Start a GitHub discussion
4. **Team Contact**: Reach out to maintainers

Thank you for contributing to the Notification Engine! 🚀
