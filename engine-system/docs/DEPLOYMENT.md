# Deployment Guide

This document provides comprehensive deployment instructions for the Notification Engine System across different environments and platforms.

## Table of Contents

- [Deployment Overview](#deployment-overview)
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Docker Deployment](#docker-deployment)
- [Production Deployment](#production-deployment)
- [Kubernetes Deployment](#kubernetes-deployment)
- [AWS Deployment](#aws-deployment)
- [Database Migration](#database-migration)
- [Monitoring and Health Checks](#monitoring-and-health-checks)
- [Scaling Considerations](#scaling-considerations)
- [Security Configuration](#security-configuration)
- [Backup and Recovery](#backup-and-recovery)
- [Troubleshooting](#troubleshooting)

## Deployment Overview

The Notification Engine System can be deployed in several ways:

- **Local Development**: Using Docker Compose for development and testing
- **Staging Environment**: Containerized deployment with external databases
- **Production**: Multi-instance deployment with load balancing and monitoring
- **Cloud Deployment**: AWS, Google Cloud, or Azure with managed services

### Architecture Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   App Instances │    │   Databases     │
│                 │    │                 │    │                 │
│ • Nginx/ALB     │───▶│ • Node.js Apps  │───▶│ • MySQL RDS     │
│ • SSL/TLS       │    │ • Docker        │    │ • Redis Cluster │
│ • Health Checks │    │ • Auto Scaling  │    │ • RabbitMQ      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

### System Requirements

- **Node.js**: Version 18+ LTS
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **MySQL**: Version 8.0+
- **Redis**: Version 6.0+
- **RabbitMQ**: Version 3.8+

### Minimum Hardware

**Development:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 10GB

**Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ (with monitoring)

## Environment Setup

### Environment Variables

Create environment files for each deployment stage:

```bash
# .env.development
NODE_ENV=development
PORT=3000
BUILD_TARGET=dev
LOGGER_TOGGLE=true

# Database
DATABASE_URL=mysql://user:password@localhost:3306/notifications_dev

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Email
EMAIL_ON=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
```

```bash
# .env.staging
NODE_ENV=staging
PORT=3000
BUILD_TARGET=prod
LOGGER_TOGGLE=true

# Database (use staging database)
DATABASE_URL=mysql://user:password@staging-db:3306/notifications_staging

# External Redis/RabbitMQ
REDIS_HOST=staging-redis.cache.amazonaws.com
RABBITMQ_HOST=staging-rabbitmq.domain.com

# Email (use test SMTP)
EMAIL_ON=true
SMTP_HOST=smtp.mailtrap.io
```

```bash
# .env.production
NODE_ENV=production
PORT=3000
BUILD_TARGET=prod
LOGGER_TOGGLE=false

# Production databases
DATABASE_URL=mysql://user:password@prod-db-cluster:3306/notifications
REDIS_HOST=prod-redis-cluster.cache.amazonaws.com
RABBITMQ_HOST=prod-rabbitmq-cluster.domain.com

# Production email
EMAIL_ON=true
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=${SENDGRID_API_KEY}

# Security
ENCRYPTION_KEY=${ENCRYPTION_KEY}
JWT_SECRET=${JWT_SECRET}
```

### Secrets Management

```bash
# Use AWS Secrets Manager, Azure Key Vault, or similar
aws secretsmanager create-secret \
  --name "notification-engine/production" \
  --description "Production secrets for Notification Engine" \
  --secret-string '{
    "DATABASE_PASSWORD": "secure-password",
    "SMTP_PASSWORD": "sendgrid-api-key",
    "ENCRYPTION_KEY": "32-char-encryption-key",
    "SLACK_CLIENT_SECRET": "slack-client-secret"
  }'
```

## Docker Deployment

### Single Container Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npx prisma generate
RUN npm run build

FROM node:18-alpine as production

WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package*.json ./

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

EXPOSE 3000
CMD ["node", "dist/server.js"]
```

### Docker Compose Development

```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mysql
      - redis
      - rabbitmq
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: notifications
      MYSQL_USER: notif_user
      MYSQL_PASSWORD: notif_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
```

### Docker Compose Production

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    image: notification-engine:latest
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        max_attempts: 3
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
    ports:
      - "3000-3002:3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - app
    restart: unless-stopped
```

## Production Deployment

### Build Process

```bash
#!/bin/bash
# build.sh

set -e

echo "Building Notification Engine..."

# Install dependencies
npm ci --only=production

# Generate Prisma client
npx prisma generate

# Build TypeScript
npm run build

# Create production image
docker build -t notification-engine:${BUILD_VERSION} .
docker tag notification-engine:${BUILD_VERSION} notification-engine:latest

echo "Build completed successfully"
```

### Database Migration

```bash
#!/bin/bash
# migrate.sh

set -e

echo "Running database migrations..."

# Backup database
mysqldump -h ${DB_HOST} -u ${DB_USER} -p${DB_PASSWORD} notifications > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migrations
npx prisma migrate deploy

# Verify migration
npx prisma migrate status

echo "Migrations completed successfully"
```

### Deployment Script

```bash
#!/bin/bash
# deploy.sh

set -e

BUILD_VERSION=${1:-latest}
ENVIRONMENT=${2:-production}

echo "Deploying Notification Engine v${BUILD_VERSION} to ${ENVIRONMENT}..."

# Pull latest code
git pull origin main

# Build application
./scripts/build.sh

# Run database migrations
./scripts/migrate.sh

# Stop existing containers
docker-compose -f docker-compose.${ENVIRONMENT}.yml down

# Start new deployment
docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d

# Health check
sleep 30
./scripts/health-check.sh

echo "Deployment completed successfully"
```

### Health Check Script

```bash
#!/bin/bash
# health-check.sh

set -e

BASE_URL=${1:-http://localhost:3000}
MAX_ATTEMPTS=10
ATTEMPT=1

echo "Performing health checks..."

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
  echo "Attempt $ATTEMPT/$MAX_ATTEMPTS"

  # Basic health check
  if curl -f -s "${BASE_URL}/up" > /dev/null; then
    echo "✓ Basic health check passed"
  else
    echo "✗ Basic health check failed"
    exit 1
  fi

  # Queue health check
  if curl -f -s "${BASE_URL}/admin/queues" > /dev/null; then
    echo "✓ Queue health check passed"
  else
    echo "✗ Queue health check failed"
    exit 1
  fi

  # Database connectivity
  if curl -f -s "${BASE_URL}/health/database" > /dev/null; then
    echo "✓ Database connectivity check passed"
    break
  else
    echo "✗ Database connectivity check failed"
    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
      exit 1
    fi
  fi

  ATTEMPT=$((ATTEMPT + 1))
  sleep 10
done

echo "All health checks passed!"
```

## Kubernetes Deployment

### Kubernetes Manifests

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: notification-engine
  labels:
    name: notification-engine
```

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-engine
  namespace: notification-engine
  labels:
    app: notification-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-engine
  template:
    metadata:
      labels:
        app: notification-engine
    spec:
      containers:
      - name: notification-engine
        image: your-registry/notification-engine:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: notification-secrets
              key: database-url
        - name: REDIS_HOST
          value: "redis-service"
        - name: RABBITMQ_HOST
          value: "rabbitmq-service"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /up
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /up
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: notification-engine
spec:
  selector:
    app: notification-engine
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer
```

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: notification-secrets
  namespace: notification-engine
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  redis-password: <base64-encoded-redis-password>
  smtp-password: <base64-encoded-smtp-password>
  encryption-key: <base64-encoded-encryption-key>
```

```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: notification-engine-hpa
  namespace: notification-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: notification-engine
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Kubernetes Deployment Script

```bash
#!/bin/bash
# k8s-deploy.sh

set -e

ENVIRONMENT=${1:-staging}
IMAGE_TAG=${2:-latest}

echo "Deploying to Kubernetes ($ENVIRONMENT) with tag $IMAGE_TAG"

# Create namespace
kubectl apply -f k8s/namespace.yaml

# Apply secrets
kubectl apply -f k8s/secrets.yaml

# Update deployment image
kubectl set image deployment/notification-engine \
  notification-engine=your-registry/notification-engine:$IMAGE_TAG \
  --namespace=notification-engine

# Apply all manifests
kubectl apply -f k8s/ --namespace=notification-engine

# Wait for rollout
kubectl rollout status deployment/notification-engine --namespace=notification-engine

# Run database migrations
kubectl exec -it deployment/notification-engine --namespace=notification-engine -- \
  npx prisma migrate deploy

echo "Kubernetes deployment completed successfully"
```

## AWS Deployment

### ECS Deployment

```json
// task-definition.json
{
  "family": "notification-engine",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/notificationEngineTaskRole",
  "containerDefinitions": [
    {
      "name": "notification-engine",
      "image": "ACCOUNT.dkr.ecr.REGION.amazonaws.com/notification-engine:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "3000"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:REGION:ACCOUNT:secret:notification-engine/production:DATABASE_URL::"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/notification-engine",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/up || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### CloudFormation Template

```yaml
# infrastructure.yml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Notification Engine Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]

Resources:
  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-notification-engine-vpc

  # RDS MySQL Instance
  DatabaseSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS database
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2

  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub ${Environment}-notification-engine-db
      DBInstanceClass: db.t3.medium
      Engine: mysql
      EngineVersion: '8.0'
      AllocatedStorage: 100
      StorageType: gp2
      DBName: notifications
      MasterUsername: admin
      MasterUserPassword: !Ref DatabasePassword
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      DBSubnetGroupName: !Ref DatabaseSubnetGroup
      BackupRetentionPeriod: 7
      MultiAZ: !If [IsProduction, true, false]
      StorageEncrypted: true

  # ElastiCache Redis
  RedisSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      Description: Subnet group for Redis cluster
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2

  RedisCluster:
    Type: AWS::ElastiCache::ReplicationGroup
    Properties:
      ReplicationGroupId: !Sub ${Environment}-notification-engine-redis
      Description: Redis cluster for notification engine
      NumCacheClusters: 2
      Engine: redis
      CacheNodeType: cache.t3.medium
      CacheSubnetGroupName: !Ref RedisSubnetGroup
      SecurityGroupIds:
        - !Ref RedisSecurityGroup
      AtRestEncryptionEnabled: true
      TransitEncryptionEnabled: true

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${Environment}-notification-engine

  # Application Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${Environment}-notification-engine-alb
      Scheme: internet-facing
      SecurityGroups:
        - !Ref LoadBalancerSecurityGroup
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2

Conditions:
  IsProduction: !Equals [!Ref Environment, production]

Outputs:
  DatabaseEndpoint:
    Description: RDS instance endpoint
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub ${Environment}-db-endpoint

  RedisEndpoint:
    Description: Redis cluster endpoint
    Value: !GetAtt RedisCluster.RedisEndpoint.Address
    Export:
      Name: !Sub ${Environment}-redis-endpoint
```

### Terraform Configuration

```hcl
# main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "${var.environment}-notification-engine"
  cidr = "10.0.0.0/16"

  azs             = ["${var.aws_region}a", "${var.aws_region}b"]
  private_subnets = ["********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24"]

  enable_nat_gateway = true
  enable_vpn_gateway = true

  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

# RDS MySQL
resource "aws_db_instance" "mysql" {
  identifier     = "${var.environment}-notification-engine"
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = var.db_instance_class

  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_encrypted     = true

  db_name  = "notifications"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.default.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment != "production"

  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

# ElastiCache Redis
resource "aws_elasticache_replication_group" "redis" {
  replication_group_id       = "${var.environment}-notification-engine"
  description                = "Redis cluster for notification engine"

  port               = 6379
  parameter_group_name = "default.redis7"
  node_type          = var.redis_node_type
  num_cache_clusters = 2

  subnet_group_name  = aws_elasticache_subnet_group.default.name
  security_group_ids = [aws_security_group.redis.id]

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true

  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

# ECS Service
resource "aws_ecs_service" "notification_engine" {
  name            = "notification-engine"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.notification_engine.arn
  desired_count   = var.app_count

  launch_type = "FARGATE"

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = module.vpc.private_subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = "notification-engine"
    container_port   = 3000
  }

  depends_on = [aws_lb_listener.app]
}
```

## Database Migration

### Migration Strategy

```bash
#!/bin/bash
# migrate.sh

set -e

ENVIRONMENT=${1:-staging}

echo "Running database migrations for $ENVIRONMENT"

# Backup database before migration
if [ "$ENVIRONMENT" = "production" ]; then
  echo "Creating database backup..."
  kubectl exec -it mysql-pod --namespace=$ENVIRONMENT -- \
    mysqldump -u root -p$MYSQL_ROOT_PASSWORD notifications_$ENVIRONMENT > backup_$(date +%Y%m%d_%H%M%S).sql
fi

# Run Prisma migrations
kubectl exec -it deployment/notification-engine --namespace=$ENVIRONMENT -- \
  npx prisma migrate deploy

# Verify migration
kubectl exec -it deployment/notification-engine --namespace=$ENVIRONMENT -- \
  npx prisma migrate status

echo "Migration completed successfully"
```

### Production Seeding

```typescript
// prisma/seed.prod.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedProduction() {
  // Only seed essential data for production

  // Channel types
  await prisma.channelType.createMany({
    data: [
      { name: 'email', description: 'Email notifications' },
      { name: 'slack', description: 'Slack notifications' },
      { name: 'hubspot', description: 'HubSpot CRM integration' }
    ],
    skipDuplicates: true
  });

  // Content types
  await prisma.contentType.createMany({
    data: [
      { name: 'html', description: 'HTML content' },
      { name: 'json', description: 'JSON content' },
      { name: 'block_kit', description: 'Slack Block Kit' }
    ],
    skipDuplicates: true
  });

  // Essential trigger types
  await prisma.triggerType.createMany({
    data: [
      { name: 'user_lifecycle', description: 'User lifecycle events' },
      { name: 'business_metrics', description: 'Business metrics and reports' },
      { name: 'system_alerts', description: 'System monitoring alerts' }
    ],
    skipDuplicates: true
  });

  console.log('Production seeding completed');
}

seedProduction()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

### Migration Rollback

```bash
#!/bin/bash
# rollback.sh

set -e

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
  echo "Usage: $0 <backup_file>"
  exit 1
fi

echo "Rolling back database from $BACKUP_FILE"

# Stop application
kubectl scale deployment notification-engine --replicas=0

# Restore database
kubectl exec -i mysql-pod -- mysql -u root -p$MYSQL_ROOT_PASSWORD notifications_prod < $BACKUP_FILE

# Start application
kubectl scale deployment notification-engine --replicas=3

# Verify health
kubectl rollout status deployment/notification-engine

echo "Rollback completed successfully"
```

## Monitoring and Health Checks

### Application Health Endpoints

```typescript
// src/routes/health.ts
import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import RedisClient from '../services/redis';
import { QueueService } from '../messaging/queue-service';

const router = Router();
const prisma = new PrismaClient();

// Basic health check
router.get('/up', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Detailed health check
router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: 'unknown',
      redis: 'unknown',
      rabbitmq: 'unknown'
    }
  };

  try {
    // Database check
    await prisma.$queryRaw`SELECT 1`;
    health.services.database = 'ok';
  } catch (error) {
    health.services.database = 'error';
    health.status = 'error';
  }

  try {
    // Redis check
    const redis = RedisClient.getInstance();
    await redis.ping();
    health.services.redis = 'ok';
  } catch (error) {
    health.services.redis = 'error';
    health.status = 'error';
  }

  try {
    // RabbitMQ check
    const queueService = QueueService.getInstance();
    await queueService.checkConnection();
    health.services.rabbitmq = 'ok';
  } catch (error) {
    health.services.rabbitmq = 'error';
    health.status = 'error';
  }

  const statusCode = health.status === 'ok' ? 200 : 503;
  res.status(statusCode).json(health);
});

// Queue metrics
router.get('/metrics/queues', async (req, res) => {
  try {
    const queueService = QueueService.getInstance();
    const metrics = await queueService.getMetrics();

    res.json({
      timestamp: new Date().toISOString(),
      queues: metrics
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to get queue metrics' });
  }
});

export default router;
```

### Prometheus Metrics

```typescript
// src/middleware/metrics.ts
import { Request, Response, NextFunction } from 'express';
import client from 'prom-client';

// Create metrics
const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const queueJobsProcessed = new client.Counter({
  name: 'queue_jobs_processed_total',
  help: 'Total number of queue jobs processed',
  labelNames: ['queue_name', 'status']
});

// Middleware
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;

    httpRequestDuration
      .labels(req.method, route, res.statusCode.toString())
      .observe(duration);

    httpRequestTotal
      .labels(req.method, route, res.statusCode.toString())
      .inc();
  });

  next();
};

// Metrics endpoint
export const metricsHandler = async (req: Request, res: Response) => {
  res.set('Content-Type', client.register.contentType);
  res.end(await client.register.metrics());
};

export { queueJobsProcessed };
```

### Monitoring Stack

```yaml
# monitoring/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml

volumes:
  prometheus_data:
  grafana_data:
```

## Scaling Considerations

### Horizontal Scaling

```yaml
# kubernetes/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-engine
  labels:
    app: notification-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-engine
  template:
    metadata:
      labels:
        app: notification-engine
    spec:
      containers:
      - name: notification-engine
        image: notification-engine:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /up
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: notification-engine-service
spec:
  selector:
    app: notification-engine
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: notification-engine-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: notification-engine
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Scaling

```sql
-- Read replicas setup
CREATE USER 'readonly'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT ON notifications.* TO 'readonly'@'%';

-- Connection pooling configuration
-- In application code:
const readOnlyPool = mysql.createPool({
  host: 'read-replica-endpoint',
  user: 'readonly',
  password: 'secure_password',
  database: 'notifications',
  connectionLimit: 10
});
```

## Security Configuration

### SSL/TLS Configuration

```bash
# Generate SSL certificates (Let's Encrypt)
certbot certonly --webroot \
  -w /var/www/html \
  -d notifications.yourcompany.com \
  --email <EMAIL> \
  --agree-tos \
  --non-interactive

# Auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### Network Security

```yaml
# k8s/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: notification-engine-policy
  namespace: notification-engine
spec:
  podSelector:
    matchLabels:
      app: notification-engine
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: mysql
    ports:
    - protocol: TCP
      port: 3306
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
```

### Security Headers

```nginx
# nginx security configuration
server {
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Hide server version
    server_tokens off;
}
```

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/notification_backup_$DATE.sql"

# Create backup directory
mkdir -p $BACKUP_DIR

# Perform backup
mysqldump -h mysql-host -u backup_user -p$MYSQL_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  notifications_prod > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Upload to S3 (optional)
aws s3 cp $BACKUP_FILE.gz s3://your-backup-bucket/mysql/

# Cleanup old backups (keep last 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### Automated Backup with Cron

```bash
# Add to crontab
# Daily backup at 2 AM
0 2 * * * /opt/scripts/backup.sh

# Weekly full backup on Sunday at 1 AM
0 1 * * 0 /opt/scripts/full-backup.sh
```

### Disaster Recovery

```bash
#!/bin/bash
# disaster-recovery.sh

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
  echo "Usage: $0 <backup_file>"
  exit 1
fi

echo "Starting disaster recovery from $BACKUP_FILE"

# Stop application
kubectl scale deployment notification-engine --replicas=0

# Restore database
gunzip -c $BACKUP_FILE | mysql -h mysql-host -u root -p$MYSQL_ROOT_PASSWORD notifications_prod

# Start application
kubectl scale deployment notification-engine --replicas=3

# Verify health
kubectl rollout status deployment/notification-engine

# Run health checks
./scripts/health-check.sh

echo "Disaster recovery completed successfully"
```

## Troubleshooting

### Common Issues

**1. Database Connection Issues**
```bash
# Check database connectivity
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;"

# Check connection pool
curl http://localhost:3000/health/database
```

**2. Queue Processing Issues**
```bash
# Check queue status
curl http://localhost:3000/admin/queues

# Check Redis connectivity
redis-cli -h $REDIS_HOST ping

# Check RabbitMQ
rabbitmqctl status
```

**3. Memory Issues**
```bash
# Monitor memory usage
docker stats notification-engine

# Check for memory leaks
curl http://localhost:3000/metrics | grep nodejs_heap
```

### Log Analysis

```bash
# Application logs
docker logs notification-engine --follow

# Structured log queries
docker logs notification-engine 2>&1 | jq '.level == "error"'

# Performance logs
docker logs notification-engine 2>&1 | jq '.responseTime > 1000'
```

### Emergency Procedures

**1. Immediate Scaling**
```bash
# Scale up immediately
docker-compose up --scale app=5

# Kubernetes scaling
kubectl scale deployment notification-engine --replicas=10
```

**2. Circuit Breaker**
```bash
# Disable email sending
curl -X POST http://localhost:3000/admin/toggle-email -d '{"enabled": false}'

# Pause queue processing
curl -X POST http://localhost:3000/admin/pause-queues
```

This deployment guide provides comprehensive instructions for deploying the Notification Engine System across various environments with proper monitoring, scaling, and troubleshooting procedures.
