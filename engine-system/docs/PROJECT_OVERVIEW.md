# Notification Engine - Project Overview

## Table of Contents

- [Introduction](#introduction)
- [System Architecture](#system-architecture)
- [Core Features](#core-features)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Key Components](#key-components)
- [Getting Started](#getting-started)
- [Documentation Index](#documentation-index)

## Introduction

The Notification Engine is a comprehensive, enterprise-grade TypeScript-based system designed to handle multi-channel notifications, trigger evaluations, and template management for SaaS applications. Built with scalability, reliability, and maintainability in mind, it serves as the backbone for automated communication across email, Slack, and CRM platforms.

### Key Benefits

- **Multi-Channel Support**: Unified interface for email, Slack, and HubSpot notifications
- **Template-Driven**: Flexible Handlebars-based templating with layout inheritance
- **Queue-Based Processing**: Reliable message processing with retry mechanisms
- **Real-Time & Scheduled**: Support for both immediate and scheduled notifications
- **Comprehensive Auditing**: Full audit trails for compliance and debugging
- **Site-Specific Customization**: Per-site configuration and preferences
- **Developer-Friendly**: Well-documented APIs and extensive testing support

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Queue System   │    │   Connectors    │
│                 │    │                 │    │                 │
│ • REST APIs     │───▶│ • BullMQ/Redis  │───▶│ • Email (SMTP)  │
│ • Webhooks      │    │ • RabbitMQ      │    │ • Slack API     │
│ • Admin Panel   │    │ • Job Queues    │    │ • HubSpot CRM   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │ Template Engine │    │   External      │
│                 │    │                 │    │   Services      │
│ • MySQL/Prisma  │    │ • Handlebars    │    │                 │
│ • Notifications │    │ • Email/Slack   │    │ • Site APIs     │
│ • Templates     │    │ • Validation    │    │ • Analytics     │
│ • Triggers      │    │ • Rendering     │    │ • Integrations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

1. **API Layer** - Express.js REST API with comprehensive middleware
2. **Queue System** - Dual-queue architecture (BullMQ + RabbitMQ)
3. **Template Engine** - Handlebars-based templating with custom helpers
4. **Database Layer** - Prisma ORM with MySQL for data persistence
5. **Connectors** - Channel-specific integrations for message delivery
6. **Job Processors** - Background workers for notification processing

## Core Features

### Notification Management
- **Multi-Channel Delivery**: Email, Slack, HubSpot CRM integration
- **Template-Based Messaging**: Handlebars templates with layout support
- **Real-Time Notifications**: Immediate delivery for urgent messages
- **Scheduled Notifications**: Cron-based scheduling for recurring messages
- **Retry Mechanisms**: Automatic retry with exponential backoff
- **Delivery Tracking**: Comprehensive logging and status tracking

### Template System
- **Handlebars Templating**: Rich templating with custom helpers
- **Layout Inheritance**: Consistent branding across channels
- **Multi-Channel Support**: Channel-specific template variations
- **Parameter Validation**: Zod schema validation for template data
- **Hot Reloading**: Development-time template updates
- **Version Control**: Template versioning and rollback capabilities

### Queue Management
- **Dual-Queue Architecture**: BullMQ for jobs, RabbitMQ for messaging
- **Job Prioritization**: Priority-based job processing
- **Concurrency Control**: Configurable worker concurrency
- **Dead Letter Queues**: Failed job handling and recovery
- **Monitoring Dashboard**: Real-time queue monitoring via Bull Board
- **Rate Limiting**: Configurable rate limits per channel

### Administration
- **Health Monitoring**: Comprehensive health checks and metrics
- **Audit Logging**: Complete audit trails for compliance
- **Configuration Management**: Environment-based configuration
- **Queue Dashboard**: Visual queue monitoring and management
- **Template Management**: CRUD operations for templates and triggers

## Technology Stack

### Core Technologies
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with comprehensive middleware
- **Database**: MySQL 8.0+ with Prisma ORM
- **Queue System**: BullMQ (Redis) + RabbitMQ
- **Template Engine**: Handlebars.js with custom helpers
- **Validation**: Zod schemas for type-safe validation

### Key Dependencies
- **@bull-board/express**: Queue monitoring dashboard
- **@slack/web-api**: Slack integration and API client
- **nodemailer**: Email sending with SMTP support
- **axios**: HTTP client for external API calls
- **helmet**: Security middleware for Express
- **ioredis**: High-performance Redis client
- **amqplib**: RabbitMQ client for message queuing
- **marked**: Markdown processing for documentation

### Development Tools
- **tsx**: TypeScript execution for development
- **copyfiles**: Build asset copying for templates
- **pino**: Structured logging with performance focus
- **dotenv**: Environment configuration management
- **prettier**: Code formatting and style consistency

## Project Structure

```
engine-system/
├── src/                        # Source code
│   ├── server.ts              # Application entry point
│   ├── app.ts                 # Express app configuration
│   ├── db.ts                  # Database connection setup
│   ├── env.constant.ts        # Environment validation
│   ├── template-engine.ts     # Template processing engine
│   ├── template-registry.ts   # Template definitions and schemas
│   ├── api/                   # REST API layer
│   │   ├── controllers/       # Request handlers and business logic
│   │   ├── routes/           # Route definitions and middleware
│   │   ├── middlewares.ts    # Custom Express middleware
│   │   ├── schemas/          # API validation schemas
│   │   └── utils/            # API utility functions
│   ├── jobs/                 # Queue job definitions
│   │   ├── evaluation/       # Site evaluation jobs
│   │   ├── trigger/          # Trigger processing jobs
│   │   ├── notification/     # Notification delivery jobs
│   │   ├── bot/             # Bot automation jobs
│   │   └── observation/     # Analytics and monitoring jobs
│   ├── messaging/           # Queue and messaging infrastructure
│   │   ├── queue-service.ts # RabbitMQ service implementation
│   │   ├── consumers/       # Message consumers
│   │   └── producers/       # Message producers
│   ├── connectors/          # External service integrations
│   │   ├── smtp.ts         # Email connector implementation
│   │   ├── slack.ts        # Slack connector and API wrapper
│   │   └── hubspot.ts      # HubSpot CRM connector
│   ├── services/           # Business logic services
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Shared utility functions
├── docs/                   # Comprehensive documentation
├── templates/              # Handlebars templates
├── prisma/                 # Database schema and migrations
├── tests/                  # Test suites
└── docker-compose.yml      # Docker development setup
```

## Key Components

### API Layer (`src/api/`)
- **Controllers**: Handle HTTP requests and coordinate business logic
- **Routes**: Define API endpoints and apply middleware
- **Middleware**: Authentication, validation, error handling
- **Schemas**: Request/response validation using Zod
- **Utils**: Common API utilities and helpers

### Job System (`src/jobs/`)
- **Evaluation Jobs**: Site evaluation and trigger checking
- **Trigger Jobs**: Trigger processing and condition evaluation
- **Notification Jobs**: Message delivery and channel routing
- **Bot Jobs**: Automated tasks and integrations
- **Observation Jobs**: Analytics and monitoring tasks

### Messaging (`src/messaging/`)
- **Queue Service**: RabbitMQ connection and management
- **Consumers**: Message processing workers
- **Producers**: Message publishing utilities

### Connectors (`src/connectors/`)
- **SMTP Connector**: Email delivery via SMTP
- **Slack Connector**: Slack API integration
- **HubSpot Connector**: CRM integration and contact management

## Getting Started

### Prerequisites
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- RabbitMQ 3.8+

### Quick Setup
```bash
# Clone and install
git clone <repository-url>
cd notification-engine/engine-system
npm install

# Environment setup
cp env.sample .env
# Edit .env with your configuration

# Database setup
npx prisma generate
npx prisma db push
npx prisma db seed

# Start development server
npm run dev
```

### Docker Setup
```bash
docker-compose up -d
```

## Documentation Index

### Core Documentation
- **[API Reference](API.md)** - Complete REST API documentation
- **[Database Schema](DATABASE.md)** - Database models and relationships
- **[Queue System](QUEUES.md)** - Queue architecture and job processing
- **[Template Engine](TEMPLATES.md)** - Template system and development
- **[Testing Guide](TESTING.md)** - Testing strategies and best practices
- **[Deployment Guide](DEPLOYMENT.md)** - Production deployment instructions
- **[Troubleshooting](TROUBLESHOOTING.md)** - Common issues and solutions

### Development Guides
- **[Development Workflow](DEVELOPMENT.md)** - Setup, coding standards, and workflows
- **[Contributing Guidelines](CONTRIBUTING.md)** - How to contribute to the project
- **[Security Guidelines](SECURITY.md)** - Security best practices and policies

### Operations
- **[Monitoring Guide](MONITORING.md)** - System monitoring and alerting
- **[Performance Tuning](PERFORMANCE.md)** - Optimization and scaling
- **[Backup & Recovery](BACKUP.md)** - Data protection and disaster recovery

---

**Next Steps**: Review the [Development Workflow](DEVELOPMENT.md) guide to set up your development environment and understand our coding standards.
