import Handlebars from 'handlebars'
import { z, ZodTypeAny } from 'zod'
import { IParameter, ParameterValidation } from './types'
import { SchemaBuilder } from './schema-builder'

export class TemplateRenderer {
    private schemaBuilder: SchemaBuilder
    constructor() {
        this.schemaBuilder = new SchemaBuilder()

        // formatDate
        Handlebars.registerHelper('formatDate', function (date, format) {
            if (!date) {
                return ''
            }
            const dateObj = new Date(date)
            if (isNaN(dateObj.getTime())) {
                return ''
            }
            if (!format) {
                return dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                })
            }
            switch (format) {
                case 'short':
                    return dateObj.toLocaleDateString('en-US')
                case 'time':
                    return dateObj.toLocaleTimeString('en-US')
                case 'datetime':
                    return dateObj.toLocaleString('en-US')
                case 'iso':
                    return dateObj.toISOString()
                default:
                    return dateObj.toLocaleTimeString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                    })
            }
        })

        // currency formatting
        Handlebars.registerHelper(
            'formatCurrency',
            function (value, currency = 'USD') {
                if (value == null) return ''

                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency,
                }).format(Number(value))
            }
        )

        // Number Formating
        Handlebars.registerHelper(
            'formatNumber',
            function (value, decimals = 0) {
                if (value == null) return ''

                return new Intl.NumberFormat('en-US', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals,
                }).format(Number(value))
            }
        )

        Handlebars.registerHelper('uppercase', function (str) {
            return str ? String(str).toUpperCase() : ''
        })

        Handlebars.registerHelper('lowercase', function (str) {
            return str ? String(str).toLowerCase() : ''
        })
        Handlebars.registerHelper('capitalize', function (str) {
            if (!str) return ''
            str = String(str)
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
        })
        Handlebars.registerHelper('join', function (array, separator = ',') {
            return Array.isArray(array) ? array.join(separator) : ''
        })
        Handlebars.registerHelper('eq', function (a, b) {
            return a === b
        })
        Handlebars.registerHelper('gt', function (a, b) {
            return a > b
        })
        Handlebars.registerHelper('gte', function (a, b) {
            return a >= b
        })
        Handlebars.registerHelper('lt', function (a, b) {
            return a < b
        })
        Handlebars.registerHelper('lte', function (a, b) {
            return a <= b
        })
        Handlebars.registerHelper('and', function (...args) {
            return args.slice(0, -1).every(Boolean)
        })
        Handlebars.registerHelper('or', function (...args) {
            return args.slice(0, -1).some(Boolean)
        })
    }

    compileTemplate(templateBody: string) {
        return Handlebars.compile(templateBody)
    }

    renderTemplate(
        templateBody: string,
        paramDefinitions: IParameter[],
        parameters: Record<string, any>
    ) {
        const {
            isValid,
            errors,
            parameters: validated,
        } = this.schemaBuilder.validateParameters(paramDefinitions, parameters)
        if (!isValid) {
            throw new Error(`Invalid parameters: ${errors.join(', ')}`)
        }
        const compiled = this.compileTemplate(templateBody)
        return compiled(validated)
    }

    previewTemplate(
        templateBody: string,
        parameters: Record<string, any>
    ): { rendered: string; hasErrors: boolean; errors: string[] } {
        try {
            const compiled = this.compileTemplate(templateBody)
            const rendered = compiled(parameters)
            return { rendered, hasErrors: false, errors: [] }
        } catch (error) {
            return {
                rendered: `<div> Error: ${(error as Error)?.message} </div>`,
                hasErrors: true,
                errors: [(error as Error).message],
            }
        }
    }
}
