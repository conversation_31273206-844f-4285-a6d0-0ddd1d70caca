import { sendSlackMessage } from './send-message'
import { TemplateRenderer } from '../templates/renderer'
import { IParameter } from '../templates/types'
import { boolean } from 'zod'

// async function run() {
//     const subject = '🚨 Deployment Failure Alert'
//     const message =
//         'The latest deployment to production failed.\nEngineers have been notified and are currently investigating the issue.'
//
//     await sendSlackMessage(36, message, subject)
// }
// run()

async function creditCard() {
    const subject = 'Your heatmap trial is ready—just one more step!'
    const message = `Hey there,

You're so close to unlocking powerful insights across your site.

Complete your signup by entering your credit card details to kick off your trial—*remember, your trial won't officially start until you do*.

Once activated, you'll gain instant access to actionable insights and analytics tailored to drive meaningful growth.

Let's get your trial activated today and start uncovering hidden revenue together!

👉 <https://portal.heatmap.com|Complete Sign-up Now>`
    await sendSlackMessage(36, subject, message)
}

async function ipBlocking() {
    const subject = 'Exclude your team visits for crystal-clear insights'
    const message = `Hi {{firstName}},

We noticed you haven't configured IP blocking yet. Implementing this helps *exclude internal traffic* and gives you accurate insights into your actual customer data.

It takes less than 2 minutes—let’s set it up now for more precise analytics!

👉 <https://intercom.help/heatmapcom/en/articles/10023965-understanding-and-using-ip-blocking|Configure IP Blocking>`
    const renderer = new TemplateRenderer()
    const messageBody = renderer.renderTemplate(
        message,
        [
            {
                name: 'firstName',
                type: 'string',
                required: true,
                exampleValue: 'John Doe',
                defaultValue: null,
                description: 'first Name',
                validations: null,
            },
        ],
        { firstName: 'Incontext Opana' }
    )

    await sendSlackMessage(36, subject, messageBody)
}

async function loginBookDemo() {
    const subject =
        'Let’s jumpstart your heatmap trial with a personalized demo'

    const message = `Hey {{firstName}},

It’s been {{daysSinceLastLogin}} days since you checked in—let's fix that!

With your scale (*over $5M GMV*), a quick, personalized demo can unlock massive opportunities. Let us show you exactly how Heatmap can boost your revenue.

👉 <https://meetings.hubspot.com/markomeco/demo?uuid=************************************|Book Your Demo>`

    await sendSlackMessage(36, subject, message)
}

async function loginCaseStudy() {
    const subject = 'See how brands boosted revenue 15% with heatmap'

    const message = `Hi {{firstName}},

It’s been a few days since your last login (*{{lastLoginDate}}*), and we miss you!

Top eCommerce brands are using Heatmap to boost their revenue by *over 15%*. See exactly how they did it—and how you can too.

📖 <https://www.heatmap.com/case-studies/how-that-works-used-heatmap-to-achieve-a-13-increase-in-revenue-per-session-for-a-growing-uk-fashion-brand|Read the Case Study>

Let’s get your trial back on track!`

    await sendSlackMessage(36, subject, message)
}

async function revenueScheduleCall() {
    const subject = 'Need help with revenue tracking? Let’s talk.'

    const message = `Hello!

Still not seeing your revenue data tracking properly?

This information is key to maximizing your site's profitability.

Schedule a quick call with our team—we’ll personally walk you through the solution, ensuring everything is properly set up.

Let's get you fully operational and capturing insights now!

👉 <https://portal.heatmap.com|Schedule Revenue Tracking Call>`

    await sendSlackMessage(36, subject, message)
}

async function revenueShareDocs() {
    const subject = "Revenue data missing? Here's your quick guide."

    const message = `Hi,

Your snippet is working, but we’re not seeing revenue data yet.

To start capturing crucial revenue insights, follow our straightforward troubleshooting guide.

Quickly resolve this and start identifying exactly which elements are driving revenue on your site.

Get your full revenue tracking working today!

👉 <https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data|Resolve Revenue Tracking>`

    await sendSlackMessage(36, subject, message)
}

async function sessionScheduleCall() {
    const subject = "Let's solve your session tracking issue together"

    const message = `Hi there,

Looks like you're still experiencing trouble tracking session data.

Accurate session tracking is crucial for understanding user behavior and improving your site’s performance. Let’s hop on a quick call and resolve this swiftly, so you can get back to optimizing your revenue.

Our team is ready to help!

👉 <https://portal.heatmap.com|Schedule Tracking Help Call>`

    await sendSlackMessage(36, subject, message)
}

async function sessionShareDocs() {
    const subject = "Sessions not tracking? Here's how to fix it fast!"

    const message = `Hey,

Great job installing the heatmap snippet!

However, we noticed your session data isn't tracking yet, which limits your visibility into customer interactions.

Don’t worry—this is usually an easy fix. Check out our detailed troubleshooting guide below and you'll be tracking sessions accurately in no time.

👉 <https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data|Fix Session Tracking>`

    await sendSlackMessage(36, subject, message)
}

async function snippetScheduleCall() {
    const subject = 'Still need help with snippet installation?'

    const message = `Hi!

Looks like you're still having trouble with installing your snippet.

We’d love to help you get started so you can start benefiting from the insights Heatmap offers. Let's schedule a quick call—our team will guide you step-by-step to ensure you're fully set up. The sooner you're set, the quicker you'll find new revenue opportunities!

👉 <https://portal.heatmap.com|Schedule Installation Help>`

    await sendSlackMessage(36, subject, message)
}

async function snippetShareDocs() {
    const subject = 'Still need help with snippet installation?'

    const message = `Hey!

We noticed your heatmap snippet isn’t installed yet, meaning you're not yet tapping into the revenue insights available at your fingertips.

Installing the snippet is straightforward and takes just a few minutes.

We've put together an easy-to-follow installation guide to help you start gaining immediate value. Don’t let another day pass without actionable insights!

Snippet Installation Guide:

{{#if (eq platform 'shopify')}}
👉 <https://44015528.hs-sites.com/share/hubspotvideo/181826246573|Snippet Installation Video>
👉 <https://intercom.help/heatmapcom/en/articles/10024056-how-to-install-heatmap-snippets-on-your-shopify-store|Shopify Installation Guide>
{{else if (eq platform 'bigcommerce')}}
👉 <https://intercom.help/heatmapcom/en/articles/10024052-heatmap-big-commerce-snippet-installation|BigCommerce Installation>
{{else if (eq platform 'wordpress')}}
👉 <https://intercom.help/heatmapcom/en/articles/10508409-wordpress-woocommerce|WordPress Installation Guide>
{{else if (eq platform 'woocommerce')}}
👉 <https://intercom.help/heatmapcom/en/articles/10508409-wordpress-woocommerce|WooCommerce Installation Guide>
{{else}}
👉 <https://44015528.hs-sites.com/share/hubspotvideo/181826246573|Snippet Installation Video>
{{/if}}`

    await sendSlackMessage(36, subject, message)
}

async function teamNotInvited() {
    const subject = 'heatmap is better with your team'

    const message = `Hey {{firstName}},

Great analytics become even more powerful when shared. Invite your team to Heatmap so you can collaborate and maximize insights together.

Inviting your team only takes seconds—let’s get everyone on board!

👉 <https://portal.heatmap.com|Invite Your Team>`

    await sendSlackMessage(36, subject, message)
}

async function weeklyEndTrial() {
    const subject = 'Your Weekly heatmap Insights are Here!'

    const message = `Hey {{firstName}},

Your trial has ended, but you've already uncovered valuable insights:
Here’s how your site ({{siteName}}) performed this week:
From _{{startDate}}_ to _{{endDate}}_

* Total Sessions: {{totalWeeklySessions}}
* Total Revenue: $ {{totalWeeklyRevenue}}
* Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}
* Average Scroll Depth: {{siteWideScrollDepthWeekly}}%

Top 5 revenue-driving pages:
{{#each top5RPSPages}}
• <{{url}}|{{name}}>
{{/each}}

Keep it up! Check out detailed insights to drive even more revenue next week.

👉 <https://portal.heatmap.com|View Weekly Insights>`

    await sendSlackMessage(36, subject, message)
}

async function weeklyMidTrial() {
    const subject = 'Your Weekly heatmap Highlights'

    const message = `Hey {{firstName}},

You're halfway through your trial, and you're already uncovering valuable insights:
Here’s how your site ({{siteName}}) performed this week:
From _{{startDate}}_ to _{{endDate}}_

* Total Sessions: {{totalWeeklySessions}}
* Total Revenue: $ {{totalWeeklyRevenue}}
* Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}
* Average Scroll Depth: {{siteWideScrollDepthWeekly}}%

Here's what's driving your revenue so far—your top 5 pages by Revenue Per Session:
{{#each top5RPSPages}}
• <{{url}}|{{name}}>
{{/each}}

Let's keep optimizing your site's performance!

👉 <https://portal.heatmap.com|See Detailed Insights>`

    await sendSlackMessage(36, subject, message)
}

async function weeklyRecurring() {
    const subject = 'Your Weekly heatmap Insights are Here!'

    const message = ` Hey {{firstName}},

Here’s how your site *({{siteName}})* performed this week:

From _{{startDate}}_ to _{{endDate}}_

*Total Sessions: {{totalWeeklySessions}}*
*Total Revenue: $ {{totalWeeklyRevenue}}*
*Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}*
*Average Scroll Depth: {{siteWideScrollDepthWeekly}}%*

Top 5 revenue-driving pages:
{{#each top5RPSPages}}
• <{{url}}|{{name}}>
{{/each}}

Keep it up! Check out detailed insights to drive even more revenue next week.

👉 <https://portal.heatmap.com|View Weekly Insights> `

    const newMessage = `Hey {{firstName}} 👋

📊 *Weekly Insights for {{siteName}}*
*{{startDate}} → {{endDate}}*

• *Total Sessions:* {{totalWeeklySessions}}
• *Total Revenue:* $ {{ totalWeeklyRevenue }}
• *RPS (Revenue/Session):* $ {{ siteWideRPSWeekly }}
• *Avg. Scroll Depth:* {{siteWideScrollDepthWeekly}}%

🔝 *Top 5 Revenue Pages:*
    {{#each top5RPSPages}}
• <{{url}}|{{name}}>
    {{/each}}

        {{#if (gte siteWideRPSWeekly 3.0)}}
🚀 *Nice work!* Your RPS is looking strong — keep pushing!
        {{else}}
📉 *Let’s improve RPS this week*. Try optimizing key sections or CTAs.
        {{/if}}

➡️ <https://portal.heatmap.com|See Full Dashboard> `

    const data: IParameter[] = [
        {
            type: 'string',
            name: 'firstName',
            defaultValue: null,
            validations: null,
            description: 'first Name',
            exampleValue: 'John Doe',
            required: true,
        },
        {
            type: 'number',
            name: 'totalWeeklySessions',
            defaultValue: null,
            validations: null,
            description: 'weekly sessions',
            exampleValue: '1000',
            required: true,
        },
        {
            type: 'string',
            name: 'totalWeeklyRevenue',
            defaultValue: null,
            validations: null,
            description: 'weekly revenue',
            exampleValue: '1,000',
            required: true,
        },
        {
            type: 'string',
            name: 'siteWideRPSWeekly',
            defaultValue: null,
            validations: null,
            description: 'site wide rps',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'string',
            name: 'siteWideScrollDepthWeekly',
            defaultValue: null,
            validations: null,
            description: 'site wide scroll depth',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'string',
            name: 'startDate',
            defaultValue: null,
            validations: null,
            description: 'start Date',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'string',
            name: 'endDate',
            defaultValue: null,
            validations: null,
            description: 'end Date',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'string',
            name: 'siteName',
            defaultValue: null,
            validations: null,
            description: 'site name',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'string',
            name: 'siteName',
            defaultValue: null,
            validations: null,
            description: 'site name',
            exampleValue: '2.47',
            required: true,
        },
        {
            type: 'array',
            name: 'top5RPSPages',
            defaultValue: '[]',
            description: 'site name',
            exampleValue: '2.47',
            required: true,
            validations: {
                items: {
                    type: 'object',
                    properties: {
                        url: {
                            type: 'string',
                            required: true,
                        },
                        rps: {
                            type: 'string',
                            required: true,
                        },
                        name: {
                            type: 'string',
                            required: true,
                        },
                        revenue: {
                            type: 'string',
                            required: true,
                        },
                    },
                },
            },
        },
    ]
    const d = {
        firstName: 'Mr. Man',
        totalWeeklySessions: 677,
        totalWeeklyRevenue: '1,674.55',
        siteWideRPSWeekly: '2.47',
        siteWideScrollDepthWeekly: '62.41',
        top5RPSPages: [
            {
                url: 'https://thejellybee.com/pages/turmeric-ginger-gummies',
                name: 'Test screenshot',
                rps: '5.38',
                revenue: '1,415.45',
            },
            {
                url: 'https://thejellybee.com',
                name: 'JellyBee  Your wellness our gummies  TheJellyBee',
                rps: '2.20',
                revenue: '459.17',
            },
            {
                url: 'https://thejellybee.com/pages/landing-page',
                name: 'Landing Page  TheJellyBee',
                rps: '2.59',
                revenue: '294.75',
            },
            {
                url: 'https://thejellybee.com/pages/report-page',
                name: 'Health Report Page – TheJellyBee',
                rps: '1.73',
                revenue: '117.85',
            },
            {
                url: 'https://thejellybee.com/products/turmeric-ginger-gummies',
                name: 'Turmeric',
                rps: '8.98',
                revenue: '215.41',
            },
        ],
        startDate: 'May 28, 2025',
        endDate: 'June 3, 2025',
        siteName: 'The Jelly Bee',
    }
    const renderer = new TemplateRenderer()

    const messageBody = renderer.renderTemplate(newMessage, data, d)
    console.log(messageBody)

    await sendSlackMessage(36, subject, messageBody)
}
