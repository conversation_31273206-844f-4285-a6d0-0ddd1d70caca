import { getSlackCredentials } from './handle-slack'
import { WebClient } from '@slack/web-api'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

export async function sendSlackMessage(
    idSite: number,
    subject: string,
    message: string,
    triggerId?: number
): Promise<void> {
    const siteDetails = await getSlackCredentials(idSite, triggerId)
    const slackChannelId: string | null = (
        siteDetails?.sitePreference?.destination as any
    )?.channel_id

    if (!siteDetails.decryptedToken || !slackChannelId) {
        return
    }
    const slackClient = new WebClient(siteDetails.decryptedToken)
    const blocks = [
        ...(subject
            ? [
                  {
                      type: 'header',
                      text: {
                          type: 'plain_text',
                          text: subject,
                      },
                  },
              ]
            : []),
        {
            type: 'section',
            text: {
                type: 'mrkdwn',
                text: message,
            },
        },
    ]
    await slackClient.chat.postMessage({
        channel: slackChannelId,
        text: subject ? `${subject}\n\n${message}` : message,
        blocks,
    })
    logger.info(`slack message sent successfully for site: ${idSite}`)
}
