import { NotificationQueue } from '../../jobs/notification/notification-queue'
import { QueueService } from '../queue-service'
import { NotificationJobData } from '../../jobs/notification/jobs'

export async function startNotificationConsumer(
    notificationQueue: NotificationQueue
) {
    const queueService = QueueService.getInstance()
    await queueService.initialize()

    const exchange = 'notifications'

    await queueService.assertExchange(exchange, 'topic')

    const channels = ['email', 'slack', 'hubspot']

    for (const channel of channels) {
        const queue = `notifications.${channel}`
        // await queueService.assertQueue(queue, { durable: true, arguments: { "x-max-length": 500}});
        await queueService.assertQueue(queue, { durable: true })
        await queueService.bindQueue(
            queue,
            exchange,
            `notifications.${channel}`
        )
        await queueService.prefetch(channel === 'email' ? 20 : 5)

        await queueService.consume(queue, async (msg) => {
            if (msg) {
                const job = JSON.parse(
                    msg.content.toString()
                ) as NotificationJobData

                // await notificationQueue.addJob(
                //     {
                //         idSite: job.idSite,
                //         trigger: job.trigger,
                //         channel: job.channel,
                //         data: job.data,
                //         type: "system"
                //     },
                //     job.priority
                // )

                if (job.type === 'scheduled') {
                    await notificationQueue.addScheduledJob(job, job.priority)
                } else {
                    await notificationQueue.addJob(job, job.priority)
                }
                await queueService.ack(msg)
            }
        })
    }
}
