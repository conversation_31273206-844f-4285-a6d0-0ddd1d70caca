import amqp, { Channel, ChannelModel, Connection, Options } from 'amqplib'
import { envConfig } from '../env.constant'
import Logger from '../utils/logger'

const RABBIT_URL = `amqp://${envConfig.rabbit.username}:${envConfig.rabbit.password}@${envConfig.rabbit.host}`

const logger = Logger.getInstance()

export class QueueService {
    private isInitialized: boolean = false
    private channel: Channel | null = null
    private connection: ChannelModel | null = null
    private static instance: QueueService
    private reconnectAttempts: number = 0
    private readonly maxReconnectAttempts: number = 5
    private readonly reconnectDelay: number = 5000
    private isClosing: boolean = false
    private isReconnecting: boolean = false

    private constructor() {}

    public static getInstance(): QueueService {
        if (!QueueService.instance) {
            QueueService.instance = new QueueService()
        }
        return QueueService.instance
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return
        try {
            this.connection = await amqp.connect({
                frameMax: 8192,
                heartbeat: 10,
                hostname: envConfig.rabbit.host,
                port: envConfig.rabbit.port,
                username: envConfig.rabbit.username,
                password: envConfig.rabbit.password,
            })
            // this.connection = await amqp.connect(RABBIT_URL)
            this.channel = await this.connection.createChannel()
            this.isInitialized = true
            this.reconnectAttempts = 0
            this.isReconnecting = false
            this.setupEventListeners()
        } catch (e) {
            logger.error('Failed to initialize queue service', e)
            console.error(e)
            this.isInitialized = false
            await this.handleReconnect()
        }
    }

    private setupEventListeners(): void {
        if (!this.connection || !this.channel) {
            return
        }
        this.connection.on('error', async (err) => {
            logger.error(err)
            this.isInitialized = false
            await this.handleReconnect()
        })
        this.connection.on('close', async () => {
            logger.warn('Connection closed')
            this.isInitialized = false
            if (!this.isClosing) {
                await this.handleReconnect()
            }
        })

        this.channel.on('error', async (err) => {
            logger.error('RabbitMQ Channel Error:', err)
            this.isInitialized = false
            await this.handleReconnect()
        })

        this.channel.on('close', async () => {
            logger.warn('RabbitMQ Channel Closed')
            this.isInitialized = false
            if (!this.isClosing) {
                await this.handleReconnect()
            }
        })
    }

    private async handleReconnect(): Promise<void> {
        if (this.isReconnecting) {
            logger.info('Reconnect already in progress, skipping...')
            return
        }
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('Max reconnect attempts reached. Shutting down')
            await this.close()
            // process.exit(1)
            throw new Error('Max reconnect attempts exceeded')
        }
        this.isReconnecting = true
        this.reconnectAttempts++
        logger.info(
            `Reconnecting to RabbitMQ (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} reconnect)`
        )
        await new Promise((resolve) => setTimeout(resolve, this.reconnectDelay))
        try {
            await this.initialize()
        } catch (e: unknown) {
            logger.error(
                `Reconnect attempt ${this.reconnectAttempts} attempt failed`,
                e
            )
            this.isReconnecting = false
            throw e
        }
        this.isReconnecting = false
    }

    async close(): Promise<void> {
        this.isClosing = true
        try {
            if (this.channel) {
                await this.channel.close()
                this.channel = null
            }
            if (this.connection) {
                await this.connection.close()
                this.connection = null
            }
            this.isInitialized = false
            logger.info('QueueService closed')
        } catch (e) {
            console.error(e)
            logger.error('Error closing QueueService:', e)
        } finally {
            this.isClosing = false
        }
    }

    private async ensureChannel(): Promise<void> {
        if (!this.isInitialized || !this.channel) {
            await this.initialize()
            if (!this.channel) {
                throw new Error('Channel Failed to initialize')
            }
        }
    }

    async ack(msg: amqp.ConsumeMessage): Promise<void> {
        await this.ensureChannel()
        this.channel!.ack(msg)
    }

    async prefetch(count: number): Promise<void> {
        await this.ensureChannel()
        await this.channel!.prefetch(count)
    }

    async assertQueue(
        queueName: string,
        options: { durable: boolean; [key: string]: any } = { durable: true }
    ): Promise<void> {
        await this.ensureChannel()
        await this.channel!.assertQueue(queueName, options)
    }

    async assertExchange(
        exchange: string,
        type: 'topic' | 'direct' | 'fanout' = 'topic',
        options: { durable: boolean; [key: string]: any } = { durable: true }
    ): Promise<void> {
        await this.ensureChannel()
        await this.channel!.assertExchange(exchange, type, options)
    }

    async bindQueue(
        queueName: string,
        exchange: string,
        routingKey: string
    ): Promise<void> {
        await this.ensureChannel()
        await this.channel!.bindQueue(queueName, exchange, routingKey)
    }

    async sendToQueue(
        queue: string,
        message: Buffer,
        options: Options.Publish = { persistent: true }
    ): Promise<void> {
        await this.ensureChannel()
        await this.assertQueue(queue)
        this.channel!.sendToQueue(queue, message, options)
    }

    async publishToExchange(
        exchange: string,
        routingKey: string,
        message: Buffer,
        type: 'topic' | 'direct' | 'fanout' = 'topic',
        options: Options.Publish = { persistent: true }
    ): Promise<void> {
        await this.ensureChannel()
        await this.channel!.assertExchange(exchange, type, { durable: true })
        this.channel!.publish(exchange, routingKey, message, options)
    }

    async consume(
        queue: string,
        onMessage: (msg: amqp.ConsumeMessage | null) => void,
        options: Options.Consume = { noAck: false }
    ): Promise<void> {
        await this.ensureChannel()
        await this.channel!.assertQueue(queue, { durable: true })
        await this.channel!.consume(queue, onMessage, options)
    }
}
