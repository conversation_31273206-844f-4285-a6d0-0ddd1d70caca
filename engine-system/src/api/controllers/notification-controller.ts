import { publishToNotificationExchange } from '../../messaging/producers'
import { Request, Response } from 'express'
import { BadRequestError, createErrorResponse } from '../utils/custom.errors'
import { z } from 'zod'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

import {
    RealNotification,
    ScheduledNotification,
    validateRealNotification,
    validateScheduledNotification,
} from '../schemas/notification'

export const sendRealNotificationController = async (
    req: Request,
    res: Response
) => {
    try {
        if (!req.body) {
            throw new BadRequestError('body is required')
        }
        let realNotification: RealNotification
        try {
            realNotification = validateRealNotification(req.body)
        } catch (error) {
            if (error instanceof z.ZodError) {
                logger.error('Validation Error', error.issues)
                throw new BadRequestError('validation Error', error.issues)
            } else {
                logger.error('Invalid JSON Payload -- Parsing Failed:', error)
                throw new BadRequestError(
                    'Invalid JSON Payload -- Parsing Failed'
                )
            }
        }

        let message
        switch (realNotification.channel) {
            case 'email':
                message = {
                    body: realNotification.body,
                    subject: realNotification.subject,
                    email: realNotification.email,
                }
                break
            case 'slack':
                message = {
                    body: realNotification.body,
                    subject: realNotification.subject,
                    idSite: realNotification.idSite,
                }
                break
            default:
                message = {}
        }
        if (Object.keys(realNotification).length === 0)
            throw new BadRequestError('Unsupported notification channel type')

        await publishToNotificationExchange({
            type: 'real-time',
            channel: realNotification.channel,
            data: message,
        })
        res.status(202).json({
            message: 'Notification queued successfully',
            channel: realNotification.channel,
        })
    } catch (err) {
        logger.error('Publish error:', err)
        return createErrorResponse(err, res)
    }
}
export const sendScheduledNotificationController = async (
    req: Request,
    res: Response
) => {
    try {
        if (!req.body) {
            throw new BadRequestError('body is required')
        }
        let scheduledNotification: ScheduledNotification
        try {
            scheduledNotification = validateScheduledNotification(req.body)
        } catch (error) {
            if (error instanceof z.ZodError) {
                logger.error('Validation Error', error.issues)
                throw new BadRequestError('validation Error', error.issues)
            } else {
                logger.error('Invalid JSON Payload -- Parsing Failed:', error)
                throw new BadRequestError(
                    'Invalid JSON Payload -- Parsing Failed'
                )
            }
        }

        let message
        switch (scheduledNotification.channel) {
            case 'email':
                message = {
                    body: scheduledNotification.body,
                    subject: scheduledNotification.subject,
                    email: scheduledNotification.email,
                }
                break
            case 'slack':
                message = {
                    body: scheduledNotification.body,
                    subject: scheduledNotification.subject,
                    idSite: scheduledNotification.idSite,
                }
                break
            default:
                message = {}
        }
        if (Object.keys(scheduledNotification).length === 0)
            throw new BadRequestError('Unsupported notification channel type')

        console.log(message)
        logger.info(`sendAt is ${scheduledNotification.sendAt}`)
        await publishToNotificationExchange({
            type: 'scheduled',
            channel: scheduledNotification.channel,
            data: message,
            date: scheduledNotification.sendAt,
        })
        res.status(202).json({
            message: 'Notification queued successfully',
            channel: scheduledNotification.channel,
        })
    } catch (err) {
        logger.error('Publish error:', err)
        return createErrorResponse(err, res)
    }
}
