import { Request, Response } from 'express'
import {
    BadRequestError,
    createErrorResponse,
    NotFoundError,
} from '../utils/custom.errors'
import RedisClient from '../../services/redis'
import Logger from '../../utils/logger'
import { envConfig } from '../../env.constant'

const redis = RedisClient.getInstance()
const logger = Logger.getInstance()

export const showDevEmailController = async (req: Request, res: Response) => {
    try {
        if (envConfig.nodeEnv === 'production') {
            throw new BadRequestError('API not supported in production mode')
        }
        const devNotificationKey = `notifications:dev:email_log`

        const response = await redis.lrange(devNotificationKey, 0, 1)

        if (response.length <= 0) {
            throw new NotFoundError(
                `No dev notification found for ${devNotificationKey}`
            )
        }
        const body = response[0]
        const parsed = JSON.parse(body)

        res.setHeader('Content-Type', 'text/html')
        res.send(parsed.body)
    } catch (error) {
        return createErrorResponse(error, res)
    }
}
