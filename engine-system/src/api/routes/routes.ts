import { Request, Response, Express, NextFunction } from 'express'
import {
    upTimeRouter,
    triggerRouter,
    slackRouter,
    realTimeRouter,
    notificationRouter,
    devRouter,
    documentationRouter,
} from './index.routes'
import { setupBullBoard } from './bullboard.routes'

export const setupRoutes = (app: Express): void => {
    app.use('/trigger', triggerRouter)
    app.use('/slack', slackRouter)
    app.use('/dev-emails', devRouter)
    app.use('/up', upTimeRouter)
    app.use('/real-time', realTimeRouter)
    app.use('/notify', notificationRouter)
    app.use('/docs', documentationRouter)

    setupBullBoard(app)
    app.use((_req: Request, res: Response, next: NextFunction): void => {
        res.status(404).json({
            message: 'Route not found',
        })
    })
}
