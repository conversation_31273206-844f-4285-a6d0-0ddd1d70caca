import { Router } from 'express'
import {
    getDocumentationIndex,
    refreshDocumentation,
    getDocumentationAPI,
    getDocumentationFile,
    getStaticDocumentation,
} from '../controllers/documentation-controller'

const documentationRouter = Router()

// Main documentation index page
documentationRouter.get('/', getDocumentationIndex)

// API endpoint for JSON response
documentationRouter.get('/api', getDocumentationAPI)

// Refresh documentation cache
documentationRouter.get('/refresh', refreshDocumentation)

// View specific documentation file
documentationRouter.get('/file/*', getDocumentationFile)

// Serve static HTML documentation site
documentationRouter.get('/site/*', getStaticDocumentation)

// Serve static documentation homepage
documentationRouter.get('/site', getStaticDocumentation)

export { documentationRouter }
