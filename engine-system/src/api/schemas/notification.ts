import { z } from 'zod'

const MIN_DELAY_MS = 5 * 60 * 1000
export const RealNotificationSchema = z
    .object({
        body: z.string(),
        subject: z.string(),
        channel: z.enum(['email', 'slack']),
        email: z.string().email().optional(),
        idSite: z.number().optional(),
    })
    .refine(
        (data) => {
            return !(data.channel === 'email' && !data.email)
        },
        {
            message: 'Email is required, when channel is "email"',
            path: ['email'],
        }
    )
    .refine(
        (data) => {
            return !(data.channel === 'slack' && !data.idSite)
        },
        {
            message: 'IdSite is required, when channel is "slack"',
            path: ['idSite'],
        }
    )

const sendAtSchema = z
    .union([
        z.string().refine(
            (val) => {
                const ts = Date.parse(val)
                return !isNaN(ts)
            },
            { message: 'sendAt must be a valid ISO 8601 string' }
        ),
        z
            .number()
            .int()
            .positive()
            .refine((val) => val >= Date.now(), {
                message: 'sendAt must be in the future',
            }),
    ])
    .refine(
        (val) => {
            const ts = typeof val === 'string' ? Date.parse(val) : val
            return ts - Date.now() > MIN_DELAY_MS
        },
        {
            message: 'sendAt must be at least 5 minutes into the future',
        }
    )

const ScheduledNotificationSchema = z
    .object({
        body: z.string(),
        subject: z.string(),
        channel: z.enum(['email', 'slack']),
        email: z.string().email().optional(),
        idSite: z.number().optional(),
        sendAt: z
            .union([
                z.string().refine(
                    (val) => {
                        const ts = Date.parse(val)
                        return !isNaN(ts)
                    },
                    { message: 'sendAt must be a valid ISO 8601 string' }
                ),
                z
                    .number()
                    .int()
                    .positive()
                    .refine((val) => val >= Date.now(), {
                        message: 'sendAt must be in the future',
                    }),
            ])
            .refine(
                (val) => {
                    const ts = typeof val === 'string' ? Date.parse(val) : val
                    return ts - Date.now() > MIN_DELAY_MS
                },
                {
                    message:
                        'sendAt must be at least 5 minutes into the future',
                }
            )
            .transform((val) => {
                return typeof val === 'string' ? new Date(val) : new Date(val)
            }),
    })
    .refine(
        (data) => {
            return !(data.channel === 'email' && !data.email)
        },
        {
            message: 'Email is required, when channel is "email"',
            path: ['email'],
        }
    )
    .refine(
        (data) => {
            return !(data.channel === 'slack' && !data.idSite)
        },
        {
            message: 'IdSite is required, when channel is "slack"',
            path: ['idSite'],
        }
    )
export type RealNotification = z.infer<typeof RealNotificationSchema>
export type ScheduledNotification = z.infer<typeof ScheduledNotificationSchema>

export function validateRealNotification(data: unknown) {
    const parsed = RealNotificationSchema.parse(data)
    return parsed as RealNotification
}

export function validateScheduledNotification(
    data: unknown
): ScheduledNotification {
    const parsed = ScheduledNotificationSchema.parse(data)
    return parsed as ScheduledNotification
}
