import { z } from 'zod'
import { NotificationQueue } from '../../jobs/notification/notification-queue'

export const RealTimeNotificationSchema = z.object({
    body: z.string(),
    subject: z.string(),
    idSite: z.number(),
    email: z.string().email(),
})

export type RealTimeNotification = z.infer<typeof RealTimeNotificationSchema>

export function validateRealTimeNotification(data: unknown) {
    const parsed = RealTimeNotificationSchema.parse(data)
    return parsed as RealTimeNotification
}
