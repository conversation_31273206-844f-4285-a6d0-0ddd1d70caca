import { SiteApi } from '../../../integrations/site-apis'
import { stringifyNestedObjects } from '../../../utils/object-utils'
import { envConfig } from '../../../env.constant'
import { omitKey } from '../../../utils/func'
import Logger from '../../../utils/logger'
import { TriggerEvaluator } from '../../../services/trigger-evaluator'
import { publishToTriggersExchangeV1 } from '../../../messaging/producers'
import Redis from 'ioredis'
import { RedisKeys } from '../../../constants/redis-keys'
import { evaluateObservation } from './gen-observation'
import { ObservationQueue } from '../../observation/observation-queue'

const observationQueue = ObservationQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})

export async function executeTrialingTriggers(
    idSite: number,
    redis: Redis
): Promise<void> {
    const siteApi = new SiteApi()
    const siteState = await siteApi.fetchSiteDetails(idSite)
    siteState.idSite = idSite
    // siteState.notificationsSent = siteState.notificationsSent || {}

    const siteStateRedisKey = RedisKeys.SITE_STATE_KEY(idSite)
    await redis.hset(siteStateRedisKey, stringifyNestedObjects(siteState))
    if (envConfig.loggerToggle) {
        const cleanedSessions = omitKey(
            siteState.tracking.sessions,
            'totalLast7Days'
        )
        const cleanedRevenue = omitKey(
            siteState.tracking.revenue,
            'totalLast7Days'
        )
        const cleanedState = {
            ...siteState,
            tracking: {
                sessions: cleanedSessions,
                revenue: cleanedRevenue,
            },
        }
        Logger.dbLogger(siteState.idSite.toString(), cleanedState)
    }
    const evaluator = new TriggerEvaluator(siteState)
    const triggerResult = await evaluator.evaluateTriggers()
    if (triggerResult) {
        await publishToTriggersExchangeV1({
            idSite: +idSite,
            data: {
                email: siteState.email,
                trialDaysElapsed: triggerResult.trialDaysElapsed,
            },
            priority: triggerResult.priority,
            trigger: triggerResult.trigger,
            category: triggerResult.category,
        })
    }
    if (envConfig.nodeEnv === 'production') {
        const siteObservation = {
            type: 'system' as const,
            data: evaluateObservation(siteState),
            idSite: siteState.idSite || idSite,
        }
        await observationQueue.addJob(siteObservation)
    }
}
