import { TRIGGER_TYPE } from '../../enums/triggers'

export interface SystemNotificationJobData {
    type: 'system'
    idSite: number
    channel: string
    data: Record<string, any>
    trigger: TRIGGER_TYPE
    priority?: number
}
export interface RealTimeNotificationJobData {
    type: 'real-time'
    channel: string
    data: Record<string, any>
    priority?: number

    // body: string
    // subject: string
    // email: string
    // idSite?: number
}
export interface ScheduledNotificationJobData {
    type: 'scheduled'
    channel: string
    data: Record<string, any>
    priority?: number
    date: Date
}

export type NotificationJobData =
    | SystemNotificationJobData
    | RealTimeNotificationJobData
    | ScheduledNotificationJobData

// export interface NotificationJobData {
//     idSite: number
//     channel: string
//     data: Record<string, any>
//     trigger: TRIGGER_TYPE
//     type: 'system' | 'custom'
// }
