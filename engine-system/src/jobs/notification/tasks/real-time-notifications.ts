import { NotificationService } from '../../../connectors/notification-service'
import Logger from '../../../utils/logger'
import { sendSlackMessage } from '../../../services/slack/send-message'

const logger = Logger.getInstance()

const notificationService = NotificationService.getInstance()
export async function sendEmail(
    email: string,
    subject: string,
    body: string
): Promise<void> {
    try {
        await notificationService.sendHtmlEmail(email, subject, body)
        logger.info('email sent', { email, subject, body })
    } catch (e) {
        logger.error(e)
    }
}

export async function sendSlack(
    idSite: number,
    subject: string,
    body: string
): Promise<void> {
    try {
        await sendSlackMessage(idSite, subject, body)
    } catch (err) {
        logger.error(err)
    }
}
