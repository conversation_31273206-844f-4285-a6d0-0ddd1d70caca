import { TRIGGER_TYPE } from '../../../enums/triggers'
import {
    NotificationJobData,
    RealTimeNotificationJobData,
    SystemNotificationJobData,
} from '../jobs'
import Logger from '../../../utils/logger'
import { TemplateEngine } from '../../../template-engine'
import Redis from 'ioredis'
import { sendEmail, sendSlack } from './real-time-notifications'
import { sendSlackMessage } from '../../../services/slack/send-message'

const logger = Logger.getInstance()

export async function handleNotification(
    data: RealTimeNotificationJobData['data'],
    channel: RealTimeNotificationJobData['channel'],
    type: NotificationJobData['type'],
    redis: Redis
) {
    const handlers: Record<
        RealTimeNotificationJobData['channel'],
        () => Promise<void>
    > = {
        email: () => handleEmail(data, redis),
        message: () => handleMessage(data, redis),
        slack: () => handleSlack(data, redis),
    }

    const handler = handlers[channel]
    if (!handler) {
        logger.warn(`Unsupported channel :${channel}`)
        return
    }
    await handler()
}
async function handleMessage(
    data: RealTimeNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Message Notification`, {})
}

async function handleSlack(
    data: RealTimeNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Slack Notification`)
    await sendSlack(data.idSite, data.subject, data.body)
}
async function handleEmail(
    data: RealTimeNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Real Time Email Notification`)
    await sendEmail(data.email, data.subject, data.body)
}
