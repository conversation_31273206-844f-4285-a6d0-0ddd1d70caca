import { Job } from 'bullmq'
import {
    RealTimeNotificationJobData,
    ScheduledNotificationJobData,
} from '../jobs'
import Redis from 'ioredis'
import { handleScheduledNotifications } from './handle-scheduled-notifications'
import Logger from '../../../utils/logger'

const logger = Logger.getInstance()

export async function processScheduledNotificationJob(
    job: Job<ScheduledNotificationJobData>,
    redis: Redis
): Promise<void> {
    const { data, type, channel } = job.data
    try {
        await handleScheduledNotifications(data, channel, type, redis)
    } catch (error) {
        logger.error(error)
    }
}
