export function secondsUntil(futureTime: Date | string): number {
    const future =
        typeof futureTime === 'string' ? new Date(futureTime) : futureTime

    const now = new Date()

    const diffMs = future.getTime() - now.getTime()
    return Math.max(Math.floor(diffMs / 1000), 0)
}
export function milliSecondsUntil(futureTime: Date | string): number {
    const future =
        typeof futureTime === 'string' ? new Date(futureTime) : futureTime

    const now = new Date()

    const diffMs = future.getTime() - now.getTime()
    return Math.max(diffMs, 0)
}
