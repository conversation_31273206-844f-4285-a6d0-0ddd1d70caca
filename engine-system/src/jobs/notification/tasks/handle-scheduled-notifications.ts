import { TRIGGER_TYPE } from '../../../enums/triggers'
import { NotificationJobData, ScheduledNotificationJobData } from '../jobs'
import Logger from '../../../utils/logger'
import { TemplateEngine } from '../../../template-engine'
import Redis from 'ioredis'
import { sendEmail, sendSlack } from './real-time-notifications'
import { sendSlackMessage } from '../../../services/slack/send-message'

const logger = Logger.getInstance()

export async function handleScheduledNotifications(
    data: ScheduledNotificationJobData['data'],
    channel: ScheduledNotificationJobData['channel'],
    type: NotificationJobData['type'],
    redis: Redis
) {
    const handlers: Record<
        ScheduledNotificationJobData['channel'],
        () => Promise<void>
    > = {
        email: () => handleEmail(data, redis),
        message: () => handleMessage(data, redis),
        slack: () => handleSlack(data, redis),
    }

    const handler = handlers[channel]
    if (!handler) {
        logger.warn(`Unsupported channel :${channel}`)
        return
    }
    await handler()
}
async function handleMessage(
    data: ScheduledNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Scheduled Message Notification`, {})
}

async function handleSlack(
    data: ScheduledNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Scheduled Slack Notification`)
    await sendSlack(data.idSite, data.subject, data.body)
}
async function handleEmail(
    data: ScheduledNotificationJobData['data'],
    redis: Redis
) {
    logger.info(`Preparing Scheduled Time Email Notification`)
    await sendEmail(data.email, data.subject, data.body)
}
