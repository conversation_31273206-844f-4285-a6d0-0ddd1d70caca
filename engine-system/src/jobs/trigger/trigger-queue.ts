import { Job, Queue, QueueE<PERSON>s, Worker } from 'bullmq'
import { TriggerJobData } from './jobs'
import Redis from 'ioredis'
import RedisClient from '../../services/redis'
import { SiteState } from '../../types/site'

import Logger from '../../utils/logger'
import { EventListenerQueue } from '../event-listener-queue'
import { TRIGGER_TYPE } from '../../enums/triggers'
import { SiteApi } from '../../integrations/site-apis'
import {
    getPreviousWeekMetrics,
    getTrialingWeeklyMetrics,
    getTriggersNotificationData,
} from './tasks/prep-data'
import { processNormalTrigger } from './tasks/process-trigger'

const logger = Logger.getInstance()

interface RedisOptions {
    host: string
    port: number
}

export class TriggerQueue extends EventListenerQueue {
    private static instance: TriggerQueue | null = null
    private queue: Queue
    protected workers: Worker[]
    protected queueEvents: QueueEvents
    private readonly redisOptions: RedisOptions
    private isInitialized: boolean = false
    private NUM_WORKERS: number = 4
    private CONCURRENCY: number = 2
    private redis: Redis

    protected readonly queueName: string = 'triggerQueue'

    constructor(redisOptions: RedisOptions) {
        super()
        this.redisOptions = redisOptions
        this.queue = new Queue(this.queueName, {
            connection: this.redisOptions,
            defaultJobOptions: {
                attempts: 3,
                backoff: { type: 'exponential', delay: 1000 },
                removeOnFail: {
                    count: 1000,
                    age: 60 * 60 * 24 * 3,
                },
                removeOnComplete: {
                    count: 1000,
                    age: 60 * 60 * 24,
                },
            },
        })
        this.queueEvents = new QueueEvents(this.queueName, {
            connection: this.redisOptions,
        })
        this.workers = this.createWorkers(
            this.redisOptions,
            this.NUM_WORKERS,
            this.CONCURRENCY
        )
        this.initListeners()
        this.redis = RedisClient.getInstance()
        this.isInitialized = true
    }

    public static getInstance(redisOptions: RedisOptions) {
        if (!TriggerQueue.instance) {
            if (!redisOptions) {
                throw new Error(
                    '' + 'First call to getInstance must provide redis config'
                )
            }
            TriggerQueue.instance = new TriggerQueue(redisOptions)
        }
        return TriggerQueue.instance
    }
    private createWorkers(
        redisOptions: RedisOptions,
        workerCount: number,
        concurrency: number = 1
    ): Worker[] {
        return Array.from(
            { length: workerCount },
            () =>
                new Worker(this.queueName, this.processJob.bind(this), {
                    connection: redisOptions,
                    concurrency: concurrency,
                })
        )
    }

    private async processJob(job: Job<TriggerJobData>): Promise<void> {
        const {
            idSite,
            trigger,
            data = {},
            priority = 1,
            channel = 'email',
        } = job.data
        await processNormalTrigger(idSite, this.redis, data, trigger, channel)
        return
    }

    private async _fetchSiteState(idSite: number): Promise<SiteState> {
        const siteApi = new SiteApi()
        return await siteApi.fetchSiteDetails(idSite)
    }

    private async fetchPreviousWeekMetrics(
        state: SiteState,
        idSite: number
    ): Promise<Record<string, any> | null> {
        return await getPreviousWeekMetrics(state, idSite)
    }

    private async prepareWeeklyMetricsData(
        state: SiteState,
        idSite: number
    ): Promise<Record<string, any> | null> {
        return await getTrialingWeeklyMetrics(state, idSite)
    }

    private async prepareNotificationData(
        trigger: TRIGGER_TYPE,
        cachedState: SiteState,
        data: Partial<TriggerJobData['data']>,
        idSite: number
    ) {
        return await getTriggersNotificationData(
            trigger,
            cachedState,
            data,
            idSite
        )
    }

    public async addJob(
        data: TriggerJobData,
        priority: number = 1
    ): Promise<void> {
        await this.queue.add('trigger', data)
    }

    public async addJobsInBulk(
        list: TriggerJobData[],
        priority: number = 1
    ): Promise<void> {
        const name = 'trigger'
        const jobs = list.map((el) => {
            return {
                data: el,
                name,
            }
        })

        await this.queue.addBulk(jobs)
    }

    public async start(): Promise<void> {
        await this.queue.waitUntilReady()
        for (const worker of this.workers) {
            await worker.waitUntilReady()
        }
        logger.info('TriggerQueue Started')
    }

    private async closeResources(): Promise<void> {
        if (!this.isInitialized) {
            logger.error('TriggerQueue not initialized, skipping shutdown')
            return
        }
        await Promise.all([
            this.workers.map((worker) => worker.close()),
            this.queue.close(),
            this.queueEvents.close(),
        ])
        this.isInitialized = false
        logger.info('TriggerQueue closed')
    }

    public static async shutdown(): Promise<void> {
        if (!TriggerQueue.instance) {
            logger.info('No TriggerQueue instance to shutdown')
            return
        }
        try {
            await TriggerQueue.instance.closeResources()
            TriggerQueue.instance = null
            logger.info('TriggerQueue closed')
        } catch (error) {
            logger.error('Failed to shutdown Trigger queue', error)
            throw error
        }
    }
    public getQueue() {
        return this.queue
    }
}
