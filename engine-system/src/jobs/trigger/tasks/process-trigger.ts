import { loadSiteStateFromCache } from '../../../utils/fakes/site-state'
import { stringifyNestedObjects } from '../../../utils/object-utils'
import { publishToNotificationExchange } from '../../../messaging/producers'
import Redis from 'ioredis'
import { SiteApi } from '../../../integrations/site-apis'
import { SiteState } from '../../../types/site'
import { getTriggersNotificationData } from './prep-data'
import { TRIGGER_TYPE } from '../../../enums/triggers'
import { RedisKeys } from '../../../constants/redis-keys'

export async function processNormalTrigger(
    idSite: number,
    redis: Redis,
    data: Record<string, any>,
    trigger: TRIGGER_TYPE,
    channel: 'email' | 'slack' | 'sms' = 'email',
    priority: number = 1
): Promise<void> {
    let cachedState = await loadSiteStateFromCache(redis, idSite)
    if (!cachedState || Object.keys(cachedState).length === 0) {
        cachedState = await fetchSiteState(idSite)
        const siteStateRedisKey = RedisKeys.SITE_STATE_KEY(idSite)
        await redis.hset(siteStateRedisKey, stringifyNestedObjects(cachedState))
    }
    const notificationData = await getTriggersNotificationData(
        trigger,
        cachedState,
        data,
        idSite
    )
    if (notificationData) {
        await publishToNotificationExchange({
            idSite: +idSite,
            trigger,
            priority,
            data: {
                email: cachedState.email || data.email,
                trialDaysElapsed:
                    cachedState.trialDaysElapsed || data.trialDaysElapsed,
                ...notificationData,
            },
            channel: channel,
            type: 'system',
        })
    }
}

async function fetchSiteState(idSite: number): Promise<SiteState> {
    const siteApi = new SiteApi()
    return await siteApi.fetchSiteDetails(idSite)
}
